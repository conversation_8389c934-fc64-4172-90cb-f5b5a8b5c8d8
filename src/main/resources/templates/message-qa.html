<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>메시지 QA 도우미</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#8B5CF6',
                        secondary: '#64748B',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444',
                        message: '#EC4899'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="relative bg-gradient-to-br from-purple-50 via-white to-pink-50 rounded-xl shadow-lg border border-purple-100 p-8 mb-8 overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="absolute top-0 left-0 w-32 h-32 bg-purple-400 rounded-full -translate-x-16 -translate-y-16"></div>
                <div class="absolute bottom-0 right-0 w-24 h-24 bg-pink-400 rounded-full translate-x-12 translate-y-12"></div>
                <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-indigo-300 rounded-full"></div>
            </div>

            <!-- Content -->
            <div class="relative z-10 flex justify-between items-start">
                <div class="flex items-start gap-4">
                    <!-- Icon -->
                    <div class="bg-gradient-to-br from-purple-500 to-pink-600 p-3 rounded-xl shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>

                    <!-- Title Section -->
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                메시지 QA Tool
                            </h1>
                            <div class="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-semibold">
                                v1.0
                            </div>
                        </div>
                        <p class="text-gray-600 text-lg font-medium">예약 메시지 관리 및 즉시 발송 도구</p>
                        <div class="flex items-center gap-4 mt-3">
                            <div class="flex items-center gap-1 text-sm text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>실시간 메시지 관리</span>
                            </div>
                            <div class="flex items-center gap-1 text-sm text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>즉시 발송</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Environment Info -->
                <div id="environmentInfo" class="text-right">
                    <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                        <div class="flex items-center justify-end gap-2 mb-2">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z"></path>
                            </svg>
                            <span class="text-sm font-medium text-gray-600">현재 실행환경</span>
                        </div>
                        <div id="environmentBadge" class="px-4 py-2 rounded-lg text-sm font-semibold shadow-sm border">
                            <span id="environmentIcon" class="text-lg"></span>
                            <span id="environmentText">로딩 중...</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-2 font-medium" id="environmentDescription"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">
            
            <!-- 1. Search Messages Section -->
            <div class="relative bg-gradient-to-br from-indigo-50 via-white to-purple-50 rounded-xl shadow-lg border border-indigo-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-indigo-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-purple-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-indigo-500 to-purple-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-xs font-semibold">GET</span>
                                <h2 class="text-xl font-bold text-gray-800">예약 메시지 검색</h2>
                            </div>
                            <p class="text-sm text-gray-600">메시지 코드, 그룹명, 메시지명으로 검색</p>
                        </div>
                    </div>

                    <form id="searchMessagesForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">메시지 코드</label>
                                <input type="text" id="messageCode" 
                                       class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white shadow-sm"
                                       placeholder="예: REWARD_POINT_EARNED">
                            </div>
                            <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                                <label class="block text-sm font-semibold text-gray-700 mb-2">메시지 그룹명</label>
                                <input type="text" id="messageGroupName" 
                                       class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white shadow-sm"
                                       placeholder="예: 리워드 알림">
                            </div>
                        </div>
                        
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">메시지 식별자/이름</label>
                            <input type="text" id="messageIdentifier" 
                                   class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white shadow-sm"
                                   placeholder="UUID 또는 메시지명">
                        </div>

                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-indigo-600 hover:to-purple-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            메시지 검색
                        </button>
                    </form>

                    <div id="searchResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                검색 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Bulk Actions Section -->
            <div class="relative bg-gradient-to-br from-pink-50 via-white to-rose-50 rounded-xl shadow-lg border border-pink-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-pink-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-rose-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-pink-500 to-rose-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-xs font-semibold">POST</span>
                                <h2 class="text-xl font-bold text-gray-800">일괄 즉시 발송</h2>
                            </div>
                            <p class="text-sm text-gray-600">여러 메시지를 한번에 즉시 발송으로 변경</p>
                        </div>
                    </div>

                    <form id="bulkSendForm" class="space-y-4">
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-2">메시지 ID 목록</label>
                            <textarea id="messageIds" rows="6"
                                      class="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent bg-white shadow-sm font-mono text-sm"
                                      placeholder="UUID를 한 줄에 하나씩 입력하세요&#10;예:&#10;123e4567-e89b-12d3-a456-************&#10;987fcdeb-51a2-43d7-8f9e-123456789abc"></textarea>
                            <p class="text-xs text-gray-500 mt-2">UUID를 한 줄에 하나씩 입력하세요</p>
                        </div>

                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-pink-500 to-rose-600 text-white py-3 px-6 rounded-lg hover:from-pink-600 hover:to-rose-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            일괄 즉시 발송
                        </button>
                    </form>

                    <div id="bulkSendResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                처리 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center hidden z-50">
            <div class="bg-white/90 backdrop-blur-md rounded-xl p-8 flex items-center space-x-4 shadow-2xl border border-white/50">
                <div class="relative">
                    <div class="animate-spin rounded-full h-8 w-8 border-4 border-purple-200"></div>
                    <div class="animate-spin rounded-full h-8 w-8 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
                </div>
                <div>
                    <span class="text-gray-800 font-semibold text-lg">처리 중...</span>
                    <p class="text-gray-600 text-sm">잠시만 기다려주세요</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Utility functions
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        function showResult(resultId, data, isSuccess = true) {
            const resultDiv = document.getElementById(resultId);
            if (!resultDiv) {
                console.error(`Result div with id '${resultId}' not found`);
                return;
            }

            const contentDiv = resultDiv.querySelector('.result-content');
            if (!contentDiv) {
                console.error(`Content div (.result-content) not found in result div '${resultId}'`);
                return;
            }

            resultDiv.classList.remove('hidden');

            if (typeof data === 'object') {
                contentDiv.innerHTML = formatJsonResponse(data);
            } else {
                contentDiv.textContent = data;
            }

            const bgClass = isSuccess ? 'bg-green-50' : 'bg-red-50';
            const textClass = isSuccess ? 'text-green-800' : 'text-red-800';

            const containerDiv = resultDiv.querySelector('.result-container');
            if (containerDiv) {
                containerDiv.className = `result-container ${bgClass} rounded-lg p-4 border border-white/50 shadow-md`;
            }

            const titleElement = resultDiv.querySelector('.result-title');
            if (titleElement) {
                titleElement.className = `result-title font-semibold ${textClass} mb-3 flex items-center gap-2`;
            }
        }

        function formatDateTime(dateString) {
            if (!dateString) return '정보 없음';

            try {
                if (dateString.includes('T') && (dateString.includes('+') || dateString.includes('Z'))) {
                    const date = new Date(dateString);
                    return date.toLocaleString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        timeZone: 'Asia/Seoul'
                    });
                } else if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    const [year, month, day] = dateString.split('-');
                    const date = new Date(year, month - 1, day);
                    return date.toLocaleDateString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short'
                    });
                } else {
                    const date = new Date(dateString);
                    return date.toLocaleString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short',
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZone: 'Asia/Seoul'
                    });
                }
            } catch (error) {
                console.error('날짜 포맷팅 오류:', error);
                return dateString;
            }
        }

        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyNotification(true);
                }).catch(err => {
                    console.warn('Clipboard API 실패, 폴백 방식 시도:', err);
                    fallbackCopyToClipboard(text);
                });
            } else {
                fallbackCopyToClipboard(text);
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            try {
                textArea.focus();
                textArea.select();
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopyNotification(true);
                } else {
                    showCopyNotification(false);
                }
            } catch (err) {
                console.error('폴백 복사 실패:', err);
                showCopyNotification(false);
            } finally {
                document.body.removeChild(textArea);
            }
        }

        function showCopyNotification(success) {
            const existingNotification = document.querySelector('.copy-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = `copy-notification fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2 transition-all duration-300 ${
                success ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;

            notification.innerHTML = success
                ? '✅ <span>메시지 ID가 복사되었습니다!</span>'
                : '❌ <span>복사에 실패했습니다. 수동으로 복사해주세요.</span>';

            document.body.appendChild(notification);

            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 10);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, success ? 2000 : 4000);
        }

        function formatJsonResponse(data) {
            if (data.success === false) {
                return `<div class="text-red-600">
                    <p><strong>오류:</strong> ${data.message}</p>
                    ${data.error ? `<p><strong>상세:</strong> ${data.error}</p>` : ''}
                    ${data.error_type ? `<p><strong>오류 타입:</strong> ${data.error_type}</p>` : ''}
                </div>`;
            }

            // Search Messages Response
            if (data.messages) {
                let html = `<div class="text-green-600 mb-4">
                    <p class="text-lg font-semibold">✅ ${data.message}</p>
                    <p class="text-sm"><strong>총 개수:</strong> ${data.total_count}개</p>
                </div>`;

                if (data.messages.length > 0) {
                    html += `<div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                        <div class="space-y-0">`;

                    data.messages.forEach((msg, index) => {
                        const borderClass = index === data.messages.length - 1 ? '' : 'border-b border-gray-200';
                        const deliveryDate = formatDateTime(msg.scheduled_delivery_date);
                        const createdDate = formatDateTime(msg.created_date);

                        html += `<div class="p-4 hover:bg-gray-50 transition-colors ${borderClass}">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-800 mb-1">${msg.message_name || '이름 없음'}</h4>
                                    <p class="text-sm text-gray-600">그룹: ${msg.message_group_name}</p>
                                    <p class="text-xs text-gray-500">코드: ${msg.message_code}</p>
                                </div>
                                <div class="text-right">
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">${msg.message_kind}</span>
                                    <p class="text-xs text-gray-500 mt-1">${msg.send_target}</p>
                                </div>
                            </div>

                            <!-- 수신자 정보 -->
                            <div class="bg-blue-50 rounded-lg p-3 mb-3">
                                <h5 class="text-xs font-semibold text-blue-800 mb-2">📱 수신자 정보</h5>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 text-xs">
                                    ${msg.user_identifier ? `<div>
                                        <p class="text-blue-600 mb-1">회원 ID</p>
                                        <p class="font-mono text-blue-800 break-all">${msg.user_identifier}</p>
                                    </div>` : ''}
                                    ${msg.non_user_identifier ? `<div>
                                        <p class="text-blue-600 mb-1">비회원 ID</p>
                                        <p class="font-mono text-blue-800 break-all">${msg.non_user_identifier}</p>
                                    </div>` : ''}
                                    ${msg.visitor_identifier ? `<div>
                                        <p class="text-blue-600 mb-1">방문자 ID</p>
                                        <p class="font-mono text-blue-800 break-all">${msg.visitor_identifier}</p>
                                    </div>` : ''}
                                    ${msg.phone ? `<div>
                                        <p class="text-blue-600 mb-1">전화번호</p>
                                        <p class="font-medium text-blue-800">${msg.phone}</p>
                                    </div>` : ''}
                                    ${!msg.user_identifier && !msg.non_user_identifier && !msg.visitor_identifier && !msg.phone ? `<div class="col-span-full">
                                        <p class="text-gray-500 text-center">수신자 정보 없음</p>
                                        <p class="text-xs text-gray-400 text-center mt-1">Debug: ${JSON.stringify({
                                            user_identifier: msg.user_identifier,
                                            non_user_identifier: msg.non_user_identifier,
                                            visitor_identifier: msg.visitor_identifier,
                                            phone: msg.phone,
                                            message_requests: msg.message_requests
                                        })}</p>
                                    </div>` : ''}
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-3 mb-3">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                                    <div>
                                        <p class="text-gray-500 mb-1">예약 발송 시간</p>
                                        <p class="font-medium text-gray-800">${deliveryDate}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500 mb-1">생성 시간</p>
                                        <p class="font-medium text-gray-800">${createdDate}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-3 mb-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-xs text-gray-500 mb-1">메시지 ID</p>
                                        <code class="text-sm font-mono text-gray-800 break-all">${msg.identifier}</code>
                                    </div>
                                    <button onclick="copyToClipboard('${msg.identifier}'); this.classList.add('animate-pulse'); setTimeout(() => this.classList.remove('animate-pulse'), 1000);"
                                            class="ml-3 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                                        📋 복사
                                    </button>
                                </div>
                            </div>

                            <div class="flex gap-2">
                                <button onclick="sendMessageImmediately('${msg.identifier}')"
                                        class="flex-1 bg-gradient-to-r from-pink-500 to-rose-600 text-white py-2 px-4 rounded-lg hover:from-pink-600 hover:to-rose-700 transition duration-200 font-medium text-sm flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    즉시 발송
                                </button>
                                <button onclick="showUpdateDateModal('${msg.identifier}', '${msg.message_name || '메시지'}')"
                                        class="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-2 px-4 rounded-lg hover:from-indigo-600 hover:to-purple-700 transition duration-200 font-medium text-sm flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    날짜 변경
                                </button>
                                <button onclick="showUpdateDateTimeModal('${msg.identifier}', '${msg.message_name || '메시지'}')"
                                        class="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white py-2 px-4 rounded-lg hover:from-emerald-600 hover:to-teal-700 transition duration-200 font-medium text-sm flex items-center justify-center gap-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    시간 변경
                                </button>
                            </div>
                        </div>`;
                    });

                    html += `</div></div>`;
                } else {
                    html += `<div class="text-center py-8 text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
                        </svg>
                        <p>검색 결과가 없습니다.</p>
                    </div>`;
                }

                return html;
            }

            // Bulk Send Response
            if (data.updated_count !== undefined) {
                return `<div class="text-green-600">
                    <div class="text-center">
                        <p class="text-lg font-semibold mb-4">✅ ${data.message}</p>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
                            <h4 class="font-semibold text-green-800 mb-3">📊 처리 결과</h4>
                            <div class="text-sm text-green-700 space-y-2">
                                <div class="flex justify-between">
                                    <span>처리된 메시지:</span>
                                    <span class="font-medium">${data.updated_count}개</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>변경된 발송 시간:</span>
                                    <span class="font-medium">${formatDateTime(data.updated_delivery_date)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            }

            // Single Message Update Response
            if (data.message_id) {
                return `<div class="text-green-600">
                    <div class="text-center">
                        <p class="text-lg font-semibold mb-4">✅ ${data.message}</p>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
                            <h4 class="font-semibold text-green-800 mb-3">📋 처리된 메시지</h4>
                            <div class="text-sm text-green-700 space-y-2">
                                <div>
                                    <p class="text-xs text-green-600 mb-1">메시지 ID</p>
                                    <code class="bg-green-100 px-2 py-1 rounded font-mono text-xs">${data.message_id}</code>
                                </div>
                                ${data.new_delivery_date ? `<div>
                                    <p class="text-xs text-green-600 mb-1">새로운 발송 시간</p>
                                    <p class="font-medium">${formatDateTime(data.new_delivery_date)}</p>
                                </div>` : ''}
                                ${data.updated_delivery_date ? `<div>
                                    <p class="text-xs text-green-600 mb-1">즉시 발송 시간</p>
                                    <p class="font-medium">${formatDateTime(data.updated_delivery_date)}</p>
                                </div>` : ''}
                            </div>
                        </div>
                    </div>
                </div>`;
            }

            // Default JSON display
            return `<pre class="bg-gray-100 p-3 rounded text-sm overflow-auto">${JSON.stringify(data, null, 2)}</pre>`;
        }

        // API Functions
        async function makeApiCall(url, method = 'GET', body = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            const response = await fetch(url, options);

            if (response.headers.get('content-type')?.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        }

        async function loadEnvironmentInfo() {
            try {
                const result = await makeApiCall('/message/environment');
                if (result.success && result.environment) {
                    const env = result.environment;
                    document.getElementById('environmentIcon').textContent = env.icon;
                    document.getElementById('environmentText').textContent = env.name;
                    document.getElementById('environmentDescription').textContent = env.description;
                    document.getElementById('environmentBadge').className = `px-4 py-2 rounded-lg text-sm font-semibold shadow-sm border ${env.color}`;
                }
            } catch (error) {
                console.error('환경 정보 로드 실패:', error);
                document.getElementById('environmentText').textContent = '환경 정보 로드 실패';
            }
        }

        async function searchMessages() {
            const messageCode = document.getElementById('messageCode').value.trim();
            const messageGroupName = document.getElementById('messageGroupName').value.trim();
            const messageIdentifier = document.getElementById('messageIdentifier').value.trim();

            if (!messageCode && !messageGroupName && !messageIdentifier) {
                alert('최소 하나의 검색 조건을 입력해주세요.');
                return;
            }

            showLoading();
            try {
                const params = new URLSearchParams();
                if (messageCode) params.append('messageCode', messageCode);
                if (messageGroupName) params.append('messageGroupName', messageGroupName);
                if (messageIdentifier) params.append('messageIdentifier', messageIdentifier);

                const result = await makeApiCall(`/message/scheduled-messages/search?${params.toString()}`);
                showResult('searchResult', result, result.success !== false);
            } catch (error) {
                console.error('메시지 검색 실패:', error);
                showResult('searchResult', { success: false, message: '메시지 검색 중 오류가 발생했습니다.', error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        async function sendMessageImmediately(messageId) {
            if (!confirm('이 메시지를 즉시 발송으로 변경하시겠습니까?')) {
                return;
            }

            showLoading();
            try {
                const result = await makeApiCall(`/message/scheduled-messages/${messageId}/send-immediately`, 'POST');
                showResult('searchResult', result, result.success !== false);

                // 성공시 검색 결과 새로고침
                if (result.success !== false) {
                    setTimeout(() => {
                        searchMessages();
                    }, 1000);
                }
            } catch (error) {
                console.error('즉시 발송 변경 실패:', error);
                showResult('searchResult', { success: false, message: '즉시 발송 변경 중 오류가 발생했습니다.', error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        async function bulkSendImmediately() {
            const messageIdsText = document.getElementById('messageIds').value.trim();
            if (!messageIdsText) {
                alert('메시지 ID를 입력해주세요.');
                return;
            }

            const messageIds = messageIdsText.split('\n')
                .map(id => id.trim())
                .filter(id => id.length > 0);

            if (messageIds.length === 0) {
                alert('유효한 메시지 ID를 입력해주세요.');
                return;
            }

            if (!confirm(`${messageIds.length}개의 메시지를 일괄 즉시 발송으로 변경하시겠습니까?`)) {
                return;
            }

            showLoading();
            try {
                const params = new URLSearchParams();
                messageIds.forEach(id => params.append('messageIds', id));

                const result = await makeApiCall(`/message/scheduled-messages/bulk-send-immediately?${params.toString()}`, 'POST');
                showResult('bulkSendResult', result, result.success !== false);

                // 성공시 입력 필드 초기화
                if (result.success !== false) {
                    document.getElementById('messageIds').value = '';
                }
            } catch (error) {
                console.error('일괄 즉시 발송 실패:', error);
                showResult('bulkSendResult', { success: false, message: '일괄 즉시 발송 중 오류가 발생했습니다.', error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        function showUpdateDateModal(messageId, messageName) {
            // 모달이 없으면 생성
            let modal = document.getElementById('updateDateModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'updateDateModal';
                modal.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center hidden z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl">
                        <div class="text-center mb-6">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 mb-4">
                                <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">예약 날짜 변경</h3>
                            <p class="text-sm text-gray-600" id="modalMessageName"></p>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">새로운 예약 날짜</label>
                            <input type="date" id="newDeliveryDate"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        </div>

                        <div class="flex space-x-3">
                            <button type="button" onclick="closeUpdateDateModal()"
                                    class="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                                취소
                            </button>
                            <button type="button" onclick="confirmUpdateDate()"
                                    class="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors font-medium">
                                변경
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            // 모달 정보 설정
            document.getElementById('modalMessageName').textContent = messageName;
            modal.dataset.messageId = messageId;

            // 오늘 날짜를 기본값으로 설정
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('newDeliveryDate').value = today;

            // 모달 표시
            modal.classList.remove('hidden');
        }

        function showUpdateDateTimeModal(messageId, messageName) {
            // 모달이 없으면 생성
            let modal = document.getElementById('updateDateTimeModal');
            if (!modal) {
                modal = document.createElement('div');
                modal.id = 'updateDateTimeModal';
                modal.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center hidden z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl">
                        <div class="text-center mb-6">
                            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-emerald-100 mb-4">
                                <svg class="h-6 w-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">예약 시간 변경</h3>
                            <p class="text-sm text-gray-600" id="modalDateTimeMessageName"></p>
                        </div>

                        <div class="space-y-4 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">새로운 예약 날짜</label>
                                <input type="date" id="newDeliveryDateTime_date"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">새로운 예약 시간</label>
                                <input type="time" id="newDeliveryDateTime_time"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="flex space-x-3">
                            <button type="button" onclick="closeUpdateDateTimeModal()"
                                    class="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                                취소
                            </button>
                            <button type="button" onclick="confirmUpdateDateTime()"
                                    class="flex-1 bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors font-medium">
                                변경
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }

            // 모달 정보 설정
            document.getElementById('modalDateTimeMessageName').textContent = messageName;
            modal.dataset.messageId = messageId;

            // 현재 날짜와 시간을 기본값으로 설정
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            document.getElementById('newDeliveryDateTime_date').value = today;
            document.getElementById('newDeliveryDateTime_time').value = currentTime;

            // 모달 표시
            modal.classList.remove('hidden');
        }

        function closeUpdateDateModal() {
            const modal = document.getElementById('updateDateModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        function closeUpdateDateTimeModal() {
            const modal = document.getElementById('updateDateTimeModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }

        async function confirmUpdateDate() {
            const modal = document.getElementById('updateDateModal');
            const messageId = modal.dataset.messageId;
            const newDate = document.getElementById('newDeliveryDate').value;

            if (!newDate) {
                alert('날짜를 선택해주세요.');
                return;
            }

            closeUpdateDateModal();
            showLoading();

            try {
                const params = new URLSearchParams();
                params.append('newDeliveryDate', newDate);

                const result = await makeApiCall(`/message/scheduled-messages/${messageId}/update-delivery-date?${params.toString()}`, 'POST');
                showResult('searchResult', result, result.success !== false);

                // 성공시 검색 결과 새로고침
                if (result.success !== false) {
                    setTimeout(() => {
                        searchMessages();
                    }, 1000);
                }
            } catch (error) {
                console.error('예약 시간 변경 실패:', error);
                showResult('searchResult', { success: false, message: '예약 시간 변경 중 오류가 발생했습니다.', error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        async function confirmUpdateDateTime() {
            const modal = document.getElementById('updateDateTimeModal');
            const messageId = modal.dataset.messageId;
            const newDate = document.getElementById('newDeliveryDateTime_date').value;
            const newTime = document.getElementById('newDeliveryDateTime_time').value;

            if (!newDate || !newTime) {
                alert('날짜와 시간을 모두 선택해주세요.');
                return;
            }

            // ISO 8601 형식으로 변환 (한국 시간대 +09:00)
            const newDateTime = `${newDate}T${newTime}:00+09:00`;

            closeUpdateDateTimeModal();
            showLoading();

            try {
                const params = new URLSearchParams();
                params.append('newDeliveryDateTime', newDateTime);

                const result = await makeApiCall(`/message/scheduled-messages/${messageId}/update-delivery-datetime?${params.toString()}`, 'POST');
                showResult('searchResult', result, result.success !== false);

                // 성공시 검색 결과 새로고침
                if (result.success !== false) {
                    setTimeout(() => {
                        searchMessages();
                    }, 1000);
                }
            } catch (error) {
                console.error('예약 시간 변경 실패:', error);
                showResult('searchResult', { success: false, message: '예약 시간 변경 중 오류가 발생했습니다.', error: error.message }, false);
            } finally {
                hideLoading();
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            // 환경 정보 로드
            loadEnvironmentInfo();

            // 검색 폼 이벤트
            document.getElementById('searchMessagesForm').addEventListener('submit', function(e) {
                e.preventDefault();
                searchMessages();
            });

            // 일괄 발송 폼 이벤트
            document.getElementById('bulkSendForm').addEventListener('submit', function(e) {
                e.preventDefault();
                bulkSendImmediately();
            });

            // 모달 외부 클릭시 닫기
            document.addEventListener('click', function(e) {
                const dateModal = document.getElementById('updateDateModal');
                const dateTimeModal = document.getElementById('updateDateTimeModal');

                if (dateModal && e.target === dateModal) {
                    closeUpdateDateModal();
                }
                if (dateTimeModal && e.target === dateTimeModal) {
                    closeUpdateDateTimeModal();
                }
            });

            // ESC 키로 모달 닫기
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeUpdateDateModal();
                    closeUpdateDateTimeModal();
                }
            });
        });
    </script>
</body>
</html>
