package com.example.qaapirepository.dataosurce

import com.zaxxer.hikari.HikariDataSource
import javax.sql.DataSource

object DatasourceFactory {

    fun createDatasource(
        jdbcUrl: String,
        username: String,
        password: String,
        poolName: String,
    ): DataSource {
        val hikariDataSource = HikariDataSource()
        hikariDataSource.driverClassName = "org.postgresql.Driver"
        hikariDataSource.username = username
        hikariDataSource.password = password
        hikariDataSource.jdbcUrl = jdbcUrl
        hikariDataSource.poolName = poolName
        hikariDataSource.maximumPoolSize = 10

        return hikariDataSource
    }
}
