<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>적립마켓 QA 전용 API 요청 페이지</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-top: 20px;
        }
        h1, h2 {
            color: #333;
        }
        form {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 50%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin-top: 20px;
        }

        /* 새로운 스타일 */
        .request-button {
            background-color: #4CAF50;
            color: white;
        }
        .clear-button {
            background-color: #7799ec;
            color: white;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>적립마켓 QA 전용 API 요청 페이지</h1>

    <br>
    <br>

    <!-- Search Users Form -->
    <h2>회원 검색</h2>
    <form id="searchUsersForm" onsubmit="document.getElementById('searchUsersButton').click(); return false;">
        <label for="keyword">검색어:</label>
        <input type="text" id="keyword" name="keyword" required><br><br>
        <button type="button" id="searchUsersButton" class="request-button" onclick="searchUsers()">Search</button>
        <button type="button" class="clear-button" onclick="clearResults('searchUsersResult')">Clear Results</button>
    </form>

    <div id="searchUsersResult" class="result"></div>

    <!-- Search Collecting Stores Form -->
    <h2>적립 매장 검색</h2>
    <form id="searchCollectingStoresForm" onsubmit="document.getElementById('searchCollectingStoresButton').click(); return false;">
        <label for="storeKeyword">검색어:</label>
        <input type="text" id="storeKeyword" name="storeKeyword" required><br><br>
        <button type="button" id="searchCollectingStoresButton" class="request-button" onclick="searchCollectingStores()">Search</button>
        <button type="button" class="clear-button" onclick="clearResults('searchCollectingStoresResult')">Clear Results</button>
    </form>

    <div id="searchCollectingStoresResult" class="result"></div>

    <!-- Clear Agreements Form -->
    <h2>회원 동의 내역 삭제</h2>
    <form id="clearAgreementsForm" onsubmit="document.getElementById('clearAgreementsButton').click(); return false;">
        <label for="agreementUserId">회원 식별자:</label>
        <input type="text" id="agreementUserId" name="agreementUserId" required><br><br>
        <button type="button" id="clearAgreementsButton" class="request-button" onclick="clearAgreements()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('clearAgreementsResult')">Clear Results</button>
    </form>

    <div id="clearAgreementsResult" class="result"></div>

    <!-- Move Point Date Form -->
    <h2>특정 회원의 포인트/스탬프/쿠폰/판매글(적립매물 포함) 날짜 이동</h2>
    <form id="movePointDateForm" onsubmit="document.getElementById('movePointDateButton').click(); return false;">
        <label for="userId">회원 식별자:</label>
        <input type="text" id="userId" name="userId" required><br><br>
        <label for="storeId">매장 식별자:</label>
        <input type="text" id="storeId" name="storeId" required><br><br>
        <label for="movedDays">이동 날짜:</label>
        <input type="number" id="movedDays" name="movedDays" required><br><br>
        <button type="button" id="movePointDateButton" class="request-button" onclick="movePointDate()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('movePointDateResult')">Clear Results</button>
    </form>

    <div id="movePointDateResult" class="result"></div>

    <!-- Accumulate Offline Collecting Form -->
    <h2>오프라인 적립</h2>
    <form id="accumulateOfflineCollectingForm" onsubmit="document.getElementById('accumulateOfflineCollectingButton').click(); return false;">
        <label for="offlineUserId">회원 식별자:</label>
        <input type="text" id="offlineUserId" name="offlineUserId" required><br><br>
        <label for="offlineStoreId">매장 식별자:</label>
        <input type="text" id="offlineStoreId" name="offlineStoreId" required><br><br>
        <label for="offlinePoint">적립할 포인트/스탬프 수량:</label>
        <input type="number" id="offlinePoint" name="offlinePoint" required><br><br>
        <label for="offlinePointKind">포인트 종류:</label>
        <select id="offlinePointKind" name="offlinePointKind" required>
            <option value="STORE_POINT">매장 포인트</option>
            <option value="STORE_STAMP">매장 스탬프</option>
        </select><br><br>
        <button type="button" id="accumulateOfflineCollectingButton" class="request-button" onclick="accumulateOfflineCollecting()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('accumulateOfflineCollectingResult')">Clear Results</button>
    </form>

    <div id="accumulateOfflineCollectingResult" class="result"></div>

    <!-- Clear All Collecting Rewards Form -->
    <h2>특정 회원의 모든 적립 리워드 삭제</h2>
    <form id="clearAllCollectingRewardsForm" onsubmit="document.getElementById('clearAllCollectingRewardsButton').click(); return false;">
        <label for="clearUserId">회원 식별자:</label>
        <input type="text" id="clearUserId" name="clearUserId" required><br><br>
        <button type="button" id="clearAllCollectingRewardsButton" class="request-button" onclick="clearAllCollectingRewards()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('clearAllCollectingRewardsResult')">Clear Results</button>
    </form>

    <div id="clearAllCollectingRewardsResult" class="result"></div>

    <!-- Clear Collecting Rewards by User and Store Form -->
    <h2>특정 회원, 특정 매장의 모든 적립 리워드 삭제</h2>
    <form id="clearCollectingRewardsByUserAndStoreForm" onsubmit="document.getElementById('clearCollectingRewardsByUserAndStoreButton').click(); return false;">
        <label for="clearUserIdByStore">회원 식별자:</label>
        <input type="text" id="clearUserIdByStore" name="clearUserIdByStore" required><br><br>
        <label for="clearStoreId">매장 식별자:</label>
        <input type="text" id="clearStoreId" name="clearStoreId" required><br><br>
        <button type="button" id="clearCollectingRewardsByUserAndStoreButton" class="request-button" onclick="clearCollectingRewardsByUserAndStore()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('clearCollectingRewardsByUserAndStoreResult')">Clear Results</button>
    </form>

    <div id="clearCollectingRewardsByUserAndStoreResult" class="result"></div>

    <!-- Update Scheduled Sales Posts to Selling Form -->
    <h2>예약 판매글. 판매 상태로 업데이트</h2>
    <form id="updateScheduledSalesPostsForm" onsubmit="document.getElementById('updateScheduledSalesPostsButton').click(); return false;">
        <label for="targetDate">대상 날짜:</label>
        <label for="targetDate" style="color: #3cb218; font-size: 14px">* 예약 판매글 ➡️ 판매 중 상태 전환 일시가 해당 날짜보다 이전인 대상에 대해 일괄 처리됩니다. </label>
        <br>
        <input type="date" id="targetDate" name="targetDate" required><br><br>
        <button type="button" id="updateScheduledSalesPostsButton" class="request-button" onclick="updateScheduledSalesPostsToSelling()">Update</button>
        <button type="button" class="clear-button" onclick="clearResults('updateScheduledSalesPostsResult')">Clear Results</button>
    </form>

    <div id="updateScheduledSalesPostsResult" class="result"></div>

    <!-- Deposit Passbank Form -->
    <h2>패스뱅크 입금</h2>
    <form id="depositPassbankForm" onsubmit="document.getElementById('depositPassbankButton').click(); return false;">
        <label for="depositUserId">회원 식별자:</label>
        <input type="text" id="depositUserId" name="depositUserId" required><br><br>
        <label for="depositAmount">입금 금액:</label>
        <input type="number" id="depositAmount" name="depositAmount" required><br><br>
        <button type="button" id="depositPassbankButton" class="request-button" onclick="depositPassbank()">Request</button>
        <button type="button" class="clear-button" onclick="clearResults('depositPassbankResult')">Clear Results</button>
    </form>

    <div id="depositPassbankResult" class="result"></div>
</div>

<script>

    async function searchUsers() {
        const keyword = document.getElementById('keyword').value;

        const response = await fetch(`/users/search?keyword=${keyword}`, {
            method: 'GET'
        });

        const result = (await response.text()).replace(/\n/g, '<br>');
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('searchUsersResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function clearAgreements() {
        const userId = document.getElementById('agreementUserId').value;

        const response = await fetch(`/users/agreements/clear?userId=${userId}`, {
            method: 'GET'
        });

        const result = await response.text();
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('clearAgreementsResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function searchCollectingStores() {
        const keyword = document.getElementById('storeKeyword').value;

        const response = await fetch(`/rewards/collecting-stores/search?keyword=${keyword}`, {
            method: 'GET'
        });

        const result = (await response.text()).replace(/\n/g, '<br>');
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('searchCollectingStoresResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function movePointDate() {
        const userId = document.getElementById('userId').value;
        const storeId = document.getElementById('storeId').value;
        const movedDays = document.getElementById('movedDays').value;

        const response = await fetch(`/rewards/collecting-rewards/move-date?userId=${userId}&storeId=${storeId}&movedDays=${movedDays}`, {
            method: 'GET'
        });

        const result = (await response.text()).replace(/\n/g, '<br>');
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('movePointDateResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    function clearResults(elementId) {
        document.getElementById(elementId).innerHTML = '';
    }

    async function clearAllCollectingRewards() {
        const userId = document.getElementById('clearUserId').value;

        const response = await fetch(`/rewards/collecting-rewards/clear-all?userId=${userId}`, {
            method: 'GET'
        });

        const result = await response.text();
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('clearAllCollectingRewardsResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function clearCollectingRewardsByUserAndStore() {
        const userId = document.getElementById('clearUserIdByStore').value;
        const storeId = document.getElementById('clearStoreId').value;

        const response = await fetch(`/rewards/collecting-rewards/clear-by-user-and-store?userId=${userId}&storeId=${storeId}`, {
            method: 'GET'
        });

        const result = (await response.text()).replace(/\n/g, '<br>');
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('clearCollectingRewardsByUserAndStoreResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function updateScheduledSalesPostsToSelling() {
        const targetDate = document.getElementById('targetDate').value;
        const encodedTargetDate = encodeURIComponent(targetDate);


        console.log("targetDate: ", targetDate);

        const response = await fetch(`/rewards/scheduled-sales-posts/update-to-selling?targetDate=${encodedTargetDate}`, {
            method: 'GET'
        });

        const result = (await response.text()).replace(/\n/g, '<br>');
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('updateScheduledSalesPostsResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function depositPassbank() {
        const userId = document.getElementById('depositUserId').value;
        const amount = document.getElementById('depositAmount').value;

        const response = await fetch(`/rewards/passbank/deposit?userId=${userId}&amount=${amount}`, {
            method: 'GET'
        });

        const result = await response.text();
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('depositPassbankResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }

    async function accumulateOfflineCollecting() {
        const userId = document.getElementById('offlineUserId').value;
        const storeId = document.getElementById('offlineStoreId').value;
        const point = document.getElementById('offlinePoint').value;
        const pointKind = document.getElementById('offlinePointKind').value;

        const response = await fetch(`/rewards/collecting-rewards/offline-collecting?userId=${userId}&storeId=${storeId}&point=${point}&pointKind=${pointKind}`, {
            method: 'GET'
        });

        const result = await response.text();
        const timestamp = new Date().toLocaleString();
        const resultDiv = document.getElementById('accumulateOfflineCollectingResult');
        resultDiv.innerHTML += `<p>[${timestamp}] ${result}</p>`;
    }
</script>

</body>
</html>
