package com.example.qaapirepository.swagger

import openapi.api.MembersControllerApi
import openapi.model.MemberRequestDto
import openapi.model.MemberResponseDto
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RestController

@RestController
class MemberSwaggerController : MembersControllerApi{

    override fun membersPost(memberRequestDto: MemberRequestDto?): ResponseEntity<MemberResponseDto> {
        return super.membersPost(memberRequestDto)
    }
}
