package com.example.qaapirepository.users

import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID
import kotlin.math.log

@RestController
class UserQAController(
    dbConnection: DBConnection,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    val monolithServiceConnection = dbConnection.monolithServiceConnection
    val eventServiceConnection = dbConnection.eventServiceConnection
    val authServiceConnection = dbConnection.authServiceConnection


    @GetMapping("/users/delete-by-phone")
    @Transactional
    fun deleteUserByPhone(
        @RequestParam("phone") phone: String,
    ): String {
        deleteMonolithUserByPhone(phone)
        deleteAuthServiceUserByPhone(phone)
        deleteEVentServiceUserByPhone(phone)

        return "Delete success. phone : ${phone}"
    }

    private fun deleteEVentServiceUserByPhone(phone: String) {
        eventServiceConnection.update("""DELETE FROM "user" WHERE phone = :phone""", mapOf("phone" to phone))
        eventServiceConnection.update("DELETE FROM non_user WHERE phone = :phone", mapOf("phone" to phone))
        eventServiceConnection.update("DELETE FROM invite_history WHERE guest_phone = :phone", mapOf("phone" to phone))
        eventServiceConnection.update(
            "DELETE FROM receipt_raffle_event_participant WHERE phone_number = :phone",
            mapOf("phone" to phone)
        )
        eventServiceConnection.update("DELETE FROM regular_customer WHERE phone = :phone", mapOf("phone" to phone))
        eventServiceConnection.update(
            "DELETE FROM regular_customer_crm_target WHERE phone = :phone",
            mapOf("phone" to phone)
        )
        eventServiceConnection.update(
            "DELETE FROM regular_customer_order WHERE phone = :phone",
            mapOf("phone" to phone)
        )
        eventServiceConnection.update(
            "DELETE FROM event_participation WHERE participant_user_phone = :phone",
            mapOf("phone" to phone)
        )
    }

    private fun deleteMonolithUserByPhone(phone: String) {
        monolithServiceConnection.update("""DELETE FROM "user" WHERE phone = :phone""", mapOf("phone" to phone))
        monolithServiceConnection.update("DELETE FROM non_user WHERE phone = :phone", mapOf("phone" to phone))
        monolithServiceConnection.update("DELETE FROM store_user WHERE phone = :phone", mapOf("phone" to phone))
        monolithServiceConnection.update("DELETE FROM store_non_user WHERE phone = :phone", mapOf("phone" to phone))
        monolithServiceConnection.update(
            "DELETE FROM place_service_user WHERE phone_number = :phone",
            mapOf("phone" to phone)
        )
        monolithServiceConnection.update(
            "DELETE FROM invite_history WHERE guest_phone = :phone",
            mapOf("phone" to phone)
        )
        monolithServiceConnection.update(
            "DELETE FROM marketing_event_target WHERE phone = :phone",
            mapOf("phone" to phone)
        )
    }

    private fun deleteAuthServiceUserByPhone(phone: String) {
        val userIdsMap = NativeQueryBuilder()
            .select("identifier")
            .from("\"user\"")
            .where("phone = :phone").addParams("phone" to phone)
            .let { authServiceConnection.queryForList(it.query, it.queryParams) }

        val userIds = userIdsMap.mapNotNull { it["identifier"] as? UUID? }

        if (userIds.isNotEmpty()) {
            NativeQueryBuilder()
                .delete(
                    "\"user\""
                )
                .where("identifier in (:userIds)").addParams("userIds" to userIds)
                .let { authServiceConnection.update(it.query, it.queryParams) }

            NativeQueryBuilder()
                .delete("user_account")
                .where("user_identifier in (:userIds)").addParams("userIds" to userIds)
                .let { authServiceConnection.update(it.query, it.queryParams) }
        }
    }

    @GetMapping("/users/search")
    fun searchUsers(
        @RequestParam keyword: String,
    ): String {

        log.info("searchUsers keyword : $keyword")

        if (keyword.isBlank() || keyword.length < 2) {
            return "keyword is blank. result not search"
        }

        val results = NativeQueryBuilder()
            .select(
                "identifier",
                "phone",
                "name",
                "nickname",
            )
            .from("\"user\"")
            .where()
                .and {
                    just("phone like :keyword")
                        .or("nickname like :keyword")
                        .addParams("keyword" to "%${keyword}%")
                }
                .and("status = :status").addParams("status" to 1)
                .and("level = :level").addParams("level" to 1)
            .let {
                monolithServiceConnection.queryForList(it.query, it.queryParams)
            }

        val builder = StringBuilder()

        results.forEach { builder.appendLine(it) }

        return """
            ========================
            $builder
            ========================
        """.trimIndent()
    }

    @GetMapping("/users/agreements/clear")
    fun clearAgreements(
        @RequestParam("userId") userId: UUID,
    ): String {

        val executeCount = NativeQueryBuilder()
            .delete("agreement")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .let { monolithServiceConnection.update(it.query, it.queryParams) }

        return "Clear success. userId : ${userId}, executeCount : $executeCount"
    }
}
