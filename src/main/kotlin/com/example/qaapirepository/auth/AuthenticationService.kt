package com.example.qaapirepository.auth

import com.fasterxml.jackson.annotation.JsonProperty
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate

@Service
class AuthenticationService(
    private val restTemplate: RestTemplate,
    @Value("\${external-api.monolith}")
    private val apiHost: String
) {
    private val loginId: String = "<EMAIL>"
    private val loginPassword: String = "FLHgOsiKhkQ9KnnadJzhn93JUuUiUQU9vXW7SSZnWKs="

    private val log = LoggerFactory.getLogger(this::class.java)

    /**
     * 게스트 로그인 후 정식 로그인을 수행하여 액세스 토큰만 반환합니다.
     * 1시간 동안 캐싱됩니다.
     *
     * @return 액세스 토큰 (String)
     */
    @Cacheable(value = ["authTokens"], key = "'loginToken'")
    fun loginWithGuestAuth(): String {
        log.info("통합 로그인 시작")

        try {
            // 1단계: 게스트 로그인
            val guestAccessToken = performGuestLogin()

            // 2단계: 실제 로그인
            val accessToken = performLogin(guestAccessToken)

            log.info("통합 로그인 성공")
            return accessToken
        } catch (e: Exception) {
            log.error("통합 로그인 실패", e)
            throw RuntimeException("통합 로그인 실패: ${e.message}")
        }
    }

    /**
     * 인증 토큰 캐시를 클리어합니다.
     */
    @CacheEvict(value = ["authTokens"], key = "'loginToken'")
    fun clearAuthTokenCache() {
        log.info("인증 토큰 캐시 클리어")
    }

    /**
     * 게스트 로그인을 수행하고 액세스 토큰을 반환합니다.
     */
    private fun performGuestLogin(): String {
        log.info("게스트 로그인 시작")

        val headers = HttpHeaders().apply {
            set("X-Platform", "BackEndMigrationProgram")
        }

        val entity = HttpEntity<Any>(headers)
        val url = "$apiHost/v2/users/authentication/guest"

        val response = restTemplate.exchange(
            url,
            HttpMethod.POST,
            entity,
            GuestLoginResponse::class.java
        )

        val guestLoginResponse = response.body
            ?: throw RuntimeException("게스트 로그인 응답이 비어있습니다.")

        log.info("게스트 로그인 성공")
        return guestLoginResponse.accessToken
    }

    /**
     * 로그인을 수행하고 액세스 토큰을 반환합니다.
     */
    private fun performLogin(guestAccessToken: String): String {
        log.info("로그인 시작")

        val headers = HttpHeaders().apply {
            set("X-Platform", "BackEndMigrationProgram")
            contentType = MediaType.APPLICATION_FORM_URLENCODED
        }

        val body = LinkedMultiValueMap<String, String>().apply {
            add("login_id", loginId)
            add("password", loginPassword)
            add("app_agent", "3")
        }

        val entity = HttpEntity(body, headers)
        val url = "$apiHost/v2/users/authentication"

        val response = restTemplate.exchange(
            url,
            HttpMethod.POST,
            entity,
            LoginResponse::class.java
        )

        val loginResponse = response.body
            ?: throw RuntimeException("로그인 응답이 비어있습니다.")

        log.info("로그인 성공")
        return loginResponse.accessToken
    }

    /**
     * 게스트 로그인 응답 데이터 클래스
     */
    private data class GuestLoginResponse(
        @JsonProperty("access_token")
        val accessToken: String,
        @JsonProperty("refresh_token")
        val refreshToken: String
    )

    /**
     * 로그인 응답 데이터 클래스
     */
    private data class LoginResponse(
        @JsonProperty("access_token")
        val accessToken: String,
        @JsonProperty("refresh_token")
        val refreshToken: String,
        val identifier: String,
        val kind: String,
        @JsonProperty("last_password_change_since_days")
        val lastPasswordChangeSinceDays: Int,
        @JsonProperty("password_change_threshold_days")
        val passwordChangeThresholdDays: Int,
        @JsonProperty("user_account_identifier")
        val userAccountIdentifier: String
    )
}
