<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>고객확인의무 QA 도우미</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#64748B',
                        success: '#10B981',
                        warning: '#F59E0B',
                        danger: '#EF4444'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-xl shadow-lg border border-gray-100 p-8 mb-8 overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <div class="absolute top-0 left-0 w-32 h-32 bg-blue-400 rounded-full -translate-x-16 -translate-y-16"></div>
                <div class="absolute bottom-0 right-0 w-24 h-24 bg-purple-400 rounded-full translate-x-12 translate-y-12"></div>
                <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-indigo-300 rounded-full"></div>
            </div>

            <!-- Content -->
            <div class="relative z-10 flex justify-between items-start">
                <div class="flex items-start gap-4">
                    <!-- Icon -->
                    <div class="bg-gradient-to-br from-blue-500 to-purple-600 p-3 rounded-xl shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>

                    <!-- Title Section -->
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                고객확인의무 QA Tool
                            </h1>
                            <div class="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-semibold">
                                v1.0
                            </div>
                        </div>
                        <p class="text-gray-600 text-lg font-medium">Customer Due Diligence 문서 관리 및 테스트 도구</p>
                        <div class="flex items-center gap-4 mt-3">
                            <div class="flex items-center gap-1 text-sm text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>실시간 API 테스트</span>
                            </div>
                            <div class="flex items-center gap-1 text-sm text-gray-500">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>문서 관리</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Environment Info -->
                <div id="environmentInfo" class="text-right">
                    <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                        <div class="flex items-center justify-end gap-2 mb-2">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z"></path>
                            </svg>
                            <span class="text-sm font-medium text-gray-600">현재 실행환경</span>
                        </div>
                        <div id="environmentBadge" class="px-4 py-2 rounded-lg text-sm font-semibold shadow-sm border">
                            <span id="environmentIcon" class="text-lg"></span>
                            <span id="environmentText">로딩 중...</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-2 font-medium" id="environmentDescription"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

            <!-- 1. Create CDD Document -->
            <div class="relative bg-gradient-to-br from-green-50 via-white to-emerald-50 rounded-xl shadow-lg border border-green-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-green-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-emerald-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-green-500 to-emerald-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">POST</span>
                                <h2 class="text-xl font-bold text-gray-800">CDD 문서 생성</h2>
                            </div>
                            <p class="text-sm text-gray-600">새로운 고객 실사 문서를 생성합니다</p>
                        </div>
                    </div>
                    <form id="createDocumentForm" class="space-y-6">
                        <!-- 매장 검색 -->
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                매장 검색
                            </label>
                            <div>
                                <input type="text" id="storeSearchInput"
                                       class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent bg-white shadow-sm"
                                       placeholder="매장명 또는 매장 ID를 입력하세요 (2글자 이상)">
                                <div id="storeSearchResults" class="mt-2 p-3 bg-green-50/80 backdrop-blur-sm rounded-lg border border-green-200">
                                    <div class="text-xs text-green-600 mb-2 flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        매장 검색 결과
                                    </div>
                                    <div id="storeSearchResultsList" class="flex flex-col gap-1 h-40 overflow-y-auto">
                                        <div class="text-xs text-gray-500 text-center py-8">
                                            <div class="mb-2">💡 검색 팁</div>
                                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                매장명 또는 매장 식별자(UUID)로 검색할 수 있습니다
                            </p>
                        </div>

                        <!-- 선택된 매장 정보 -->
                        <div id="selectedStoreInfo" class="hidden">
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center gap-2 mb-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <h4 class="font-semibold text-blue-800">선택된 매장</h4>
                                </div>
                                <p class="text-sm text-blue-700 font-medium" id="selectedStoreName"></p>
                                <p class="text-xs text-blue-600 font-mono" id="selectedStoreId"></p>
                                <button type="button" onclick="clearSelectedStore()"
                                        class="text-xs text-blue-600 hover:text-blue-800 mt-2 font-medium underline">선택 해제</button>
                            </div>
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 px-6 rounded-lg hover:from-green-600 hover:to-emerald-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            CDD 문서 생성
                        </button>
                    </form>
                    <div id="createResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                응답 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2. Find Documents -->
            <div class="relative bg-gradient-to-br from-blue-50 via-white to-cyan-50 rounded-xl shadow-lg border border-blue-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-blue-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-cyan-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-blue-500 to-cyan-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-semibold">GET</span>
                                <h2 class="text-xl font-bold text-gray-800">문서 검색</h2>
                            </div>
                            <p class="text-sm text-gray-600">매장별 CDD 문서를 조회합니다</p>
                        </div>
                    </div>
                    <form id="findDocumentsForm" class="space-y-6">
                        <!-- 매장 검색 -->
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                매장 검색
                            </label>
                            <div>
                                <input type="text" id="findStoreSearchInput"
                                       class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
                                       placeholder="매장명 또는 매장 ID를 입력하세요 (2글자 이상)">
                                <div id="findStoreSearchResults" class="mt-2 p-3 bg-blue-50/80 backdrop-blur-sm rounded-lg border border-blue-200">
                                    <div class="text-xs text-blue-600 mb-2 flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        매장 검색 결과
                                    </div>
                                    <div id="findStoreSearchResultsList" class="flex flex-col gap-1 h-40 overflow-y-auto">
                                        <div class="text-xs text-gray-500 text-center py-8">
                                            <div class="mb-2">💡 검색 팁</div>
                                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                매장명 또는 매장 식별자(UUID)로 검색할 수 있습니다
                            </p>
                        </div>

                        <!-- 선택된 매장 정보 -->
                        <div id="findSelectedStoreInfo" class="hidden">
                            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                                <div class="flex items-center gap-2 mb-2">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <h4 class="font-semibold text-blue-800">선택된 매장</h4>
                                </div>
                                <p class="text-sm text-blue-700 font-medium" id="findSelectedStoreName"></p>
                                <p class="text-xs text-blue-600 font-mono" id="findSelectedStoreId"></p>
                                <div class="flex items-center justify-between mt-2">
                                    <button type="button" onclick="clearFindSelectedStore()"
                                            class="text-xs text-blue-600 hover:text-blue-800 font-medium underline">선택 해제</button>
                                    <button type="button" onclick="showDeleteAllModal()"
                                            class="text-xs text-gray-300 hover:text-gray-600 font-medium underline cursor-pointer">
                                        문서가 너무 많아서 곤란하신가요?
                                    </button>
                                </div>
                            </div>
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-3 px-6 rounded-lg hover:from-blue-600 hover:to-cyan-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            문서 검색
                        </button>
                    </form>
                    <div id="findResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                검색 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 3. Expire Document -->
            <div class="relative bg-gradient-to-br from-orange-50 via-white to-yellow-50 rounded-xl shadow-lg border border-orange-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-orange-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-yellow-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-orange-500 to-yellow-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-xs font-semibold">POST</span>
                                <h2 class="text-xl font-bold text-gray-800">문서 만료</h2>
                            </div>
                            <p class="text-sm text-gray-600">CDD 문서를 만료 처리합니다</p>
                        </div>
                    </div>

                    <form id="expireDocumentForm" class="space-y-6">
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 0v4m0-4h4m-4 0H8m9 5a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Document ID
                            </label>
                            <div>
                                <input type="text" id="expireDocumentId"
                                       class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-white shadow-sm font-mono"
                                       placeholder="예: fcb22321-a2f2-4011-8b47-427c965340a3">
                                <div id="expireDocumentSuggestions" class="mt-2 p-3 bg-orange-50/80 backdrop-blur-sm rounded-lg border border-orange-200">
                                    <div class="text-xs text-orange-600 mb-2 flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        최근 사용한 문서 ID
                                    </div>
                                    <div id="expireDocumentSuggestionsList" class="flex flex-col gap-1 h-40 overflow-y-auto">
                                        <div class="text-xs text-gray-500 text-center py-8">
                                            <div class="mb-2">📄 Document ID</div>
                                            <div>• UUID 형식의 문서 식별자</div>
                                            <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                만료 처리할 문서의 고유 식별자를 입력하세요
                            </p>
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-orange-500 to-yellow-600 text-white py-3 px-6 rounded-lg hover:from-orange-600 hover:to-yellow-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            문서 만료 처리
                        </button>
                    </form>
                    <div id="expireResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                응답 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 4. Modify Due Date -->
            <div class="relative bg-gradient-to-br from-purple-50 via-white to-indigo-50 rounded-xl shadow-lg border border-purple-100 p-6 overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-purple-400 rounded-full translate-x-10 -translate-y-10"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-indigo-300 rounded-full -translate-x-8 translate-y-8"></div>
                </div>

                <div class="relative z-10">
                    <div class="flex items-center gap-3 mb-6">
                        <div class="bg-gradient-to-br from-purple-500 to-indigo-600 p-2 rounded-lg shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center gap-2 mb-1">
                                <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs font-semibold">POST</span>
                                <h2 class="text-xl font-bold text-gray-800">만료일 수정</h2>
                            </div>
                            <p class="text-sm text-gray-600">CDD 문서의 만료일을 변경합니다</p>
                        </div>
                    </div>

                    <form id="modifyDueDateForm" class="space-y-6">
                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Document ID
                            </label>
                            <div>
                                <input type="text" id="modifyDocumentId"
                                       class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white shadow-sm font-mono"
                                       placeholder="예: fcb22321-a2f2-4011-8b47-427c965340a3">
                                <div id="modifyDocumentSuggestions" class="mt-2 p-3 bg-gray-50/80 backdrop-blur-sm rounded-lg border border-gray-200">
                                    <div class="text-xs text-gray-600 mb-2 flex items-center gap-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        최근 사용한 문서 ID
                                    </div>
                                    <div id="modifyDocumentSuggestionsList" class="flex flex-col gap-1 h-40 overflow-y-auto">
                                        <div class="text-xs text-gray-500 text-center py-8">
                                            <div class="mb-2">📄 Document ID</div>
                                            <div>• UUID 형식의 문서 식별자</div>
                                            <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                만료일을 수정할 문서의 고유 식별자를 입력하세요
                            </p>
                        </div>

                        <div class="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                새로운 만료일
                            </label>
                            <input type="date" id="modifyDueDate"
                                   class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white shadow-sm">
                            <p class="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                문서의 새로운 만료일을 선택하세요
                            </p>
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white py-3 px-6 rounded-lg hover:from-purple-600 hover:to-indigo-700 transition duration-200 font-semibold shadow-lg flex items-center justify-center gap-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            만료일 수정
                        </button>
                    </form>
                    <div id="modifyResult" class="mt-6 hidden">
                        <div class="result-container bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-white/50 shadow-md">
                            <h4 class="result-title font-semibold text-gray-800 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                수정 결과
                            </h4>
                            <div class="result-content text-sm text-gray-700"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center hidden z-50">
            <div class="bg-white/90 backdrop-blur-md rounded-xl p-8 flex items-center space-x-4 shadow-2xl border border-white/50">
                <div class="relative">
                    <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-200"></div>
                    <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
                </div>
                <div>
                    <span class="text-gray-800 font-semibold text-lg">처리 중...</span>
                    <p class="text-gray-600 text-sm">잠시만 기다려주세요</p>
                </div>
            </div>
        </div>

        <!-- Delete All Documents Modal -->
        <div id="deleteAllModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center hidden z-50">
            <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl">
                <div class="text-center mb-6">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">모든 문서 삭제</h3>
                    <div class="text-sm text-gray-600 mb-4 text-center">
                        <p class="mb-2">선택한 매장의 모든 CDD 문서를 삭제하시겠습니까?</p>
                        <p class="text-xs text-red-600 font-medium">본 QA Tool 은 승인 이후의 결과에 대해서 책임지지 않습니다. 👍 🙈🙉</p>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="text-sm space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">매장명:</span>
                            <span class="font-medium" id="modalStoreName">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">매장 ID:</span>
                            <span class="font-mono text-sm" id="modalStoreId">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">문서 개수:</span>
                            <span class="font-medium text-red-600" id="modalDocumentCount">조회 중...</span>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="closeDeleteAllModal()"
                            class="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                        취소
                    </button>
                    <button type="button" onclick="confirmDeleteAll()"
                            class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium">
                        모든 문서 삭제
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Utility functions
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        function copyToClipboard(text) {
            // 현대적인 Clipboard API 시도
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyNotification(true);
                }).catch(err => {
                    console.warn('Clipboard API 실패, 폴백 방식 시도:', err);
                    fallbackCopyToClipboard(text);
                });
            } else {
                // 폴백 방식 사용
                fallbackCopyToClipboard(text);
            }
        }

        function fallbackCopyToClipboard(text) {
            // 임시 textarea 생성
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            try {
                textArea.focus();
                textArea.select();
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopyNotification(true);
                } else {
                    showCopyNotification(false);
                }
            } catch (err) {
                console.error('폴백 복사 실패:', err);
                showCopyNotification(false);
            } finally {
                document.body.removeChild(textArea);
            }
        }

        function showCopyNotification(success) {
            // 기존 알림이 있으면 제거
            const existingNotification = document.querySelector('.copy-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = `copy-notification fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2 transition-all duration-300 ${
                success
                    ? 'bg-green-500 text-white'
                    : 'bg-red-500 text-white'
            }`;

            notification.innerHTML = success
                ? '📋 <span>문서 ID가 복사되었습니다!</span>'
                : '❌ <span>복사에 실패했습니다. 수동으로 복사해주세요.</span>';

            document.body.appendChild(notification);

            // 애니메이션 효과
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';

            // 슬라이드 인 애니메이션
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            }, 10);

            // 자동 제거
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, success ? 2000 : 4000); // 실패 시 더 오래 표시
        }

        function showResult(resultId, data, isSuccess = true) {
            const resultDiv = document.getElementById(resultId);
            if (!resultDiv) {
                console.error(`Result div with id '${resultId}' not found`);
                return;
            }

            // 새로운 클래스명으로 정확하게 선택
            const contentDiv = resultDiv.querySelector('.result-content');
            if (!contentDiv) {
                console.error(`Content div (.result-content) not found in result div '${resultId}'`);
                return;
            }

            resultDiv.classList.remove('hidden');

            if (typeof data === 'object') {
                if (resultId === 'findResult' || resultId === 'modifyResult' || resultId === 'createResult' || resultId === 'expireResult') {
                    contentDiv.innerHTML = formatJsonResponse(data);
                } else {
                    // 기타 결과는 JSON을 텍스트로 표시
                    contentDiv.textContent = JSON.stringify(data, null, 2);
                }
            } else {
                contentDiv.textContent = data;
            }

            // Add success/error styling - 클래스명으로 정확하게 선택
            const bgClass = isSuccess ? 'bg-green-50' : 'bg-red-50';
            const textClass = isSuccess ? 'text-green-800' : 'text-red-800';

            // 컨테이너 div
            const containerDiv = resultDiv.querySelector('.result-container');
            if (containerDiv) {
                containerDiv.className = `result-container ${bgClass} rounded-md p-3`;
            }

            // 제목 요소
            const titleElement = resultDiv.querySelector('.result-title');
            if (titleElement) {
                titleElement.className = `result-title font-medium ${textClass} mb-2`;
            }
        }

        // 날짜 포맷팅 헬퍼 함수
        function formatDateTime(dateString) {
            if (!dateString) return '정보 없음';

            try {
                // OffsetDateTime 형식 (예: 2024-07-11T15:30:45+09:00) 처리
                if (dateString.includes('T') && (dateString.includes('+') || dateString.includes('Z'))) {
                    const date = new Date(dateString);
                    return date.toLocaleString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        timeZone: 'Asia/Seoul'
                    });
                }
                // 날짜 문자열이 'YYYY-MM-DD' 형식인 경우 한국 시간대로 처리
                else if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    // 날짜만 있는 경우 한국 시간대 기준으로 처리
                    const [year, month, day] = dateString.split('-');
                    const date = new Date(year, month - 1, day); // 로컬 시간대 기준
                    return date.toLocaleDateString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short'
                    });
                } else {
                    // 기타 형식인 경우
                    const date = new Date(dateString);
                    return date.toLocaleString('ko-KR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        weekday: 'short',
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZone: 'Asia/Seoul'
                    });
                }
            } catch (error) {
                console.error('날짜 포맷팅 오류:', error);
                return dateString; // 원본 문자열 반환
            }
        }

        function formatJsonResponse(data) {
            if (data.success === false) {
                return `<div class="text-red-600">
                    <p><strong>오류:</strong> ${data.message}</p>
                    ${data.error ? `<p><strong>상세:</strong> ${data.error}</p>` : ''}
                    ${data.error_type ? `<p><strong>오류 타입:</strong> ${data.error_type}</p>` : ''}
                    ${data.store_id ? `<p><strong>매장 ID:</strong> ${data.store_id}</p>` : ''}
                </div>`;
            }

            // CDD Document Creation Response
            if (data.store_id && data.api_response) {
                const statusColor = data.api_response.status_code === 200 || data.api_response.status_code === 201 ? 'text-green-600' : 'text-yellow-600';
                return `<div class="${statusColor}">
                    <div class="text-center">
                        <p class="text-lg font-semibold mb-4">✅ ${data.message}</p>
                        ${data.document_id ? `<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
                            <h4 class="font-semibold text-yellow-800 mb-3">📄 생성된 문서</h4>
                            <div class="flex items-center gap-2 mb-3">
                                <p class="text-sm font-medium">문서 ID:</p>
                                <code class="bg-yellow-100 px-3 py-2 rounded font-mono text-sm flex-1 text-center">${data.document_id}</code>
                                <button onclick="copyToClipboard('${data.document_id}'); this.classList.add('animate-pulse'); setTimeout(() => this.classList.remove('animate-pulse'), 1000);"
                                        class="bg-yellow-200 hover:bg-yellow-300 px-3 py-2 rounded text-xs font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                                    📋 복사
                                </button>
                            </div>
                            <p class="text-xs text-yellow-700 text-center">💡 이 문서 ID를 사용하여 문서 만료나 만료일 수정을 할 수 있습니다.</p>
                        </div>` : ''}
                    </div>
                </div>`;
            }

            if (data.documents) {
                // Find documents response
                let html = `<div class="text-green-600 mb-4">
                    <p class="text-lg font-semibold">✅ ${data.message}</p>
                    <p class="text-sm"><strong>총 개수:</strong> ${data.total_count}개</p>
                </div>`;

                if (data.documents.length > 0) {
                    html += `<div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                        <div class="space-y-0">`;

                    data.documents.forEach((doc, index) => {
                        const borderClass = index === data.documents.length - 1 ? '' : 'border-b border-gray-200';
                        html += `<div class="p-4 hover:bg-gray-50 transition-colors ${borderClass}">
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-800 mb-1">${doc.store_name}</h4>
                                    <p class="text-sm text-gray-600">매장 ID: ${doc.store_id}</p>
                                </div>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">${doc.document_status}</span>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-3 mb-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <p class="text-xs text-gray-500 mb-1">문서 ID</p>
                                        <code class="text-sm font-mono text-gray-800 break-all">${doc.document_id}</code>
                                    </div>
                                    <button onclick="copyToClipboard('${doc.document_id}'); this.classList.add('animate-pulse'); setTimeout(() => this.classList.remove('animate-pulse'), 1000);"
                                            class="ml-3 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                                        📋 복사
                                    </button>
                                </div>
                            </div>

                            <div class="text-xs text-gray-500 space-y-1">
                                <p>생성일: ${formatDateTime(doc.document_created_date)}</p>
                                ${doc.document_due_date ? `<p>만료일: ${formatDateTime(doc.document_due_date)}</p>` : '<p>만료일: 미설정</p>'}
                            </div>
                        </div>`;
                    });

                    html += `</div></div>`;
                } else {
                    html += `<div class="text-center py-8 text-gray-500">
                        <p>검색된 문서가 없습니다.</p>
                    </div>`;
                }
                return html;
            }

            if (data.document_id && data.date_changes) {
                // Modify due date response
                const daysMoved = data.days_moved;
                const direction = daysMoved > 0 ? '연장' : '단축';
                const directionColor = daysMoved > 0 ? 'text-blue-600' : 'text-orange-600';
                const directionIcon = daysMoved > 0 ? '📅➡️' : '📅⬅️';

                return `<div class="text-green-600">
                    <div class="text-center mb-4">
                        <p class="text-lg font-semibold">✅ ${data.message}</p>
                        <div class="flex items-center justify-center gap-2 mt-2">
                            <span class="${directionColor} font-medium">${directionIcon} ${Math.abs(daysMoved)}일 ${direction}</span>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="font-semibold text-blue-800 mb-3 text-center">📄 문서 정보</h4>
                        <div class="text-center">
                            <p class="text-sm text-blue-600">문서 ID</p>
                            <code class="bg-blue-100 px-3 py-1 rounded font-mono text-sm">${data.document_id}</code>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- 변경 전 -->
                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-700 mb-3 text-center">📋 변경 전</h4>
                            <div class="space-y-2 text-sm">
                                <div class="bg-white rounded p-2">
                                    <p class="text-xs text-gray-500 mb-1">생성일시</p>
                                    <p class="font-medium">${formatDateTime(data.date_changes.original_created_date)}</p>
                                </div>
                                <div class="bg-white rounded p-2">
                                    <p class="text-xs text-gray-500 mb-1">만료일시</p>
                                    <p class="font-medium">${formatDateTime(data.date_changes.original_due_date)}</p>
                                </div>
                            </div>
                        </div>

                        <!-- 변경 후 -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-semibold text-green-700 mb-3 text-center">✨ 변경 후</h4>
                            <div class="space-y-2 text-sm">
                                <div class="bg-white rounded p-2">
                                    <p class="text-xs text-green-600 mb-1">생성일시</p>
                                    <p class="font-medium">${formatDateTime(data.date_changes.new_created_date)}</p>
                                </div>
                                <div class="bg-white rounded p-2">
                                    <p class="text-xs text-green-600 mb-1">만료일시</p>
                                    <p class="font-medium">${formatDateTime(data.date_changes.new_due_date)}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4 text-center">
                        <p class="text-sm text-yellow-700">
                            <strong>처리 결과:</strong> ${data.rows_updated}개 행이 업데이트되었습니다.
                        </p>
                    </div>
                </div>`;
            }

            // CDD Document Expiration Response
            if (data.document_id && data.status_change) {
                const statusColor = data.api_response.status_code === 200 ? 'text-orange-600' : 'text-red-600';
                return `<div class="${statusColor}">
                    <div class="text-center mb-4">
                        <p class="text-lg font-semibold">⚠️ ${data.message}</p>
                    </div>

                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                        <h4 class="font-semibold text-orange-800 mb-3 text-center">📄 문서 정보</h4>
                        <div class="text-center mb-3">
                            <p class="text-sm text-orange-600 mb-1">문서 ID</p>
                            <div class="flex items-center justify-center gap-2">
                                <code class="bg-orange-100 px-3 py-1 rounded font-mono text-sm">${data.document_id}</code>
                                <button onclick="copyToClipboard('${data.document_id}'); this.classList.add('animate-pulse'); setTimeout(() => this.classList.remove('animate-pulse'), 1000);"
                                        class="bg-orange-200 hover:bg-orange-300 px-2 py-1 rounded text-xs font-medium transition-all duration-200 hover:scale-105 active:scale-95">
                                    📋 복사
                                </button>
                            </div>
                        </div>

                        <div class="bg-white rounded-lg p-3">
                            <h5 class="font-medium text-orange-800 mb-2 text-center">📊 상태 변경</h5>
                            <div class="flex items-center justify-center gap-3">
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    ${data.status_change.from}
                                </span>
                                <span class="text-orange-600">→</span>
                                <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                                    ${data.status_change.to}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4 text-center">
                        <p class="text-sm text-yellow-700">
                            💡 <strong>안내:</strong> 문서가 만료 처리되어 더 이상 유효하지 않습니다.
                        </p>
                    </div>
                </div>`;
            }

            return JSON.stringify(data, null, 2);
        }

        // API call functions
        async function makeApiCall(url, method = 'GET', body = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            const response = await fetch(url, options);

            if (response.headers.get('content-type')?.includes('application/json')) {
                return await response.json();
            } else {
                return await response.text();
            }
        }

        // Store search functionality
        let searchTimeout;
        let findSearchTimeout;
        let selectedStoreId = null;
        let findSelectedStoreId = null;

        // 검색어 캐싱 관련
        const SEARCH_HISTORY_KEY = 'cdd_store_search_history';
        const DOCUMENT_HISTORY_KEY = 'cdd_document_search_history';
        const MAX_SEARCH_HISTORY = 10;
        let saveHistoryTimeout;

        // 검색 기록 관리 함수들
        function getSearchHistory() {
            try {
                const history = localStorage.getItem(SEARCH_HISTORY_KEY);
                return history ? JSON.parse(history) : [];
            } catch (e) {
                console.error('Failed to load search history:', e);
                return [];
            }
        }

        function saveSearchHistory(keyword) {
            try {
                if (!keyword || keyword.length < 2) return;

                // 기존 타이머 취소
                clearTimeout(saveHistoryTimeout);

                // 5초 후에 저장
                saveHistoryTimeout = setTimeout(() => {
                    let history = getSearchHistory();
                    // 중복 제거
                    history = history.filter(item => item !== keyword);
                    // 맨 앞에 추가
                    history.unshift(keyword);
                    // 최대 개수 제한
                    history = history.slice(0, MAX_SEARCH_HISTORY);

                    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
                    console.log('Search history saved:', keyword);
                }, 5000);
            } catch (e) {
                console.error('Failed to save search history:', e);
            }
        }

        function showSearchSuggestions(inputId, resultsId) {
            const history = getSearchHistory();
            const listDiv = document.getElementById(resultsId + 'List');

            if (history.length === 0) {
                if (listDiv) {
                    if (inputId.includes('store') || inputId.includes('Store')) {
                        listDiv.innerHTML = `
                            <div class="text-xs text-gray-500 text-center py-8">
                                <div class="mb-2">💡 검색 팁</div>
                                <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                                <div>• 최근 검색어가 자동으로 표시됩니다</div>
                            </div>
                        `;
                    } else {
                        listDiv.innerHTML = `
                            <div class="text-xs text-gray-500 text-center py-8">
                                <div class="mb-2">📄 Document ID</div>
                                <div>• UUID 형식의 문서 식별자</div>
                                <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                            </div>
                        `;
                    }
                }
                return;
            }

            let html = '';
            history.forEach(keyword => {
                html += `
                    <div class="px-3 py-2 hover:bg-white/80 cursor-pointer rounded border border-gray-200/50 flex items-center justify-between transition-colors"
                         onclick="selectSuggestion('${inputId}', '${keyword}')">
                        <div class="flex items-center gap-2">
                            <span class="text-purple-500">🕒</span>
                            <span class="text-gray-700">${keyword}</span>
                        </div>
                        <button onclick="event.stopPropagation(); event.preventDefault(); removeSearchHistory('${keyword}', '${inputId}', '${resultsId}')"
                                class="text-gray-400 hover:text-red-500 text-xs ml-2">✕</button>
                    </div>
                `;
            });

            if (listDiv) {
                listDiv.innerHTML = html;
            }
        }

        function selectSuggestion(inputId, keyword) {
            document.getElementById(inputId).value = keyword;
            document.getElementById(inputId).dispatchEvent(new Event('input'));
        }

        function saveSearchHistoryImmediately(keyword) {
            try {
                if (!keyword || keyword.length < 2) return;

                // 기존 타이머 취소 (즉시 저장하므로)
                clearTimeout(saveHistoryTimeout);

                let history = getSearchHistory();
                // 중복 제거
                history = history.filter(item => item !== keyword);
                // 맨 앞에 추가
                history.unshift(keyword);
                // 최대 개수 제한
                history = history.slice(0, MAX_SEARCH_HISTORY);

                localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
                console.log('Search history saved immediately:', keyword);
            } catch (e) {
                console.error('Failed to save search history immediately:', e);
            }
        }

        // Document ID 검색 기록 관리 함수들
        function getDocumentHistory() {
            try {
                const history = localStorage.getItem(DOCUMENT_HISTORY_KEY);
                return history ? JSON.parse(history) : [];
            } catch (e) {
                console.error('Failed to load document history:', e);
                return [];
            }
        }

        function saveDocumentHistory(documentId) {
            try {
                if (!documentId || documentId.length < 10) return; // UUID 최소 길이 체크

                let history = getDocumentHistory();
                // 중복 제거
                history = history.filter(item => item !== documentId);
                // 맨 앞에 추가
                history.unshift(documentId);
                // 최대 개수 제한
                history = history.slice(0, MAX_SEARCH_HISTORY);

                localStorage.setItem(DOCUMENT_HISTORY_KEY, JSON.stringify(history));
                console.log('Document history saved:', documentId);
            } catch (e) {
                console.error('Failed to save document history:', e);
            }
        }

        function showDocumentSuggestions(inputId, resultsId) {
            const history = getDocumentHistory();
            const listDiv = document.getElementById(resultsId + 'List');

            if (history.length === 0) {
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">📄 Document ID</div>
                            <div>• UUID 형식의 문서 식별자</div>
                            <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
                return;
            }

            let html = '';
            history.forEach(documentId => {
                html += `
                    <div class="px-3 py-2 hover:bg-white/80 cursor-pointer rounded border border-gray-200/50 flex items-center justify-between transition-colors"
                         onclick="selectDocumentSuggestion('${inputId}', '${documentId}')">
                        <div class="flex items-center gap-2">
                            <span class="text-purple-500">📄</span>
                            <code class="text-gray-700 text-sm font-mono">${documentId}</code>
                        </div>
                        <button onclick="event.stopPropagation(); event.preventDefault(); removeDocumentHistory('${documentId}', '${inputId}', '${resultsId}')"
                                class="text-gray-400 hover:text-red-500 text-xs ml-2">✕</button>
                    </div>
                `;
            });

            if (listDiv) {
                listDiv.innerHTML = html;
            }
        }

        function selectDocumentSuggestion(inputId, documentId) {
            document.getElementById(inputId).value = documentId;

            // 추천 검색어를 기본 상태로 되돌리기
            if (inputId === 'expireDocumentId') {
                const listDiv = document.getElementById('expireDocumentSuggestionsList');
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">📄 Document ID</div>
                            <div>• UUID 형식의 문서 식별자</div>
                            <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
            } else if (inputId === 'modifyDocumentId') {
                const listDiv = document.getElementById('modifyDocumentSuggestionsList');
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">📄 Document ID</div>
                            <div>• UUID 형식의 문서 식별자</div>
                            <div>• 최근 사용한 문서가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
            }
        }

        function removeDocumentHistory(documentId, inputId, resultsId) {
            try {
                let history = getDocumentHistory();
                history = history.filter(item => item !== documentId);
                localStorage.setItem(DOCUMENT_HISTORY_KEY, JSON.stringify(history));

                // 항상 추천 검색어 다시 표시 (activeElement 조건 제거)
                showDocumentSuggestions(inputId, resultsId);
            } catch (e) {
                console.error('Failed to remove document history:', e);
            }
        }

        function removeSearchHistory(keyword, inputId, resultsId) {
            try {
                let history = getSearchHistory();
                history = history.filter(item => item !== keyword);
                localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));

                // 항상 추천 검색어 다시 표시 (activeElement 조건 제거)
                showSearchSuggestions(inputId, resultsId);
            } catch (e) {
                console.error('Failed to remove search history:', e);
            }
        }

        function clearSelectedStore() {
            selectedStoreId = null;
            document.getElementById('selectedStoreInfo').classList.add('hidden');
        }

        function selectStore(storeId, storeName) {
            selectedStoreId = storeId;
            document.getElementById('selectedStoreName').textContent = storeName;
            document.getElementById('selectedStoreId').textContent = `ID: ${storeId}`;
            document.getElementById('selectedStoreInfo').classList.remove('hidden');

            // 검색 결과를 기본 상태로 되돌리기
            const listDiv = document.getElementById('storeSearchResultsList');
            if (listDiv) {
                listDiv.innerHTML = `
                    <div class="text-xs text-gray-500 text-center py-8">
                        <div class="mb-2">💡 검색 팁</div>
                        <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                        <div>• 최근 검색어가 자동으로 표시됩니다</div>
                    </div>
                `;
            }

            // 매장 선택 시 검색어 즉시 저장
            const searchInput = document.getElementById('storeSearchInput');
            const searchKeyword = searchInput.value.trim();
            if (searchKeyword.length >= 2) {
                saveSearchHistoryImmediately(searchKeyword);
            }

            searchInput.value = '';
        }

        async function searchStores(keyword) {
            const listDiv = document.getElementById('storeSearchResultsList');

            if (keyword.length < 2) {
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">💡 검색 팁</div>
                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
                return;
            }

            // 검색 기록 저장
            saveSearchHistory(keyword);

            try {
                const result = await makeApiCall(`/cdd/stores/search?keyword=${encodeURIComponent(keyword)}`);
                const resultsDiv = document.getElementById('storeSearchResults');

                if (result.success && result.stores.length > 0) {
                    const listDiv = document.getElementById('storeSearchResultsList');
                    let html = '';

                    result.stores.forEach(store => {
                        html += `
                            <div class="px-3 py-2 hover:bg-white/80 cursor-pointer rounded border border-gray-200/50 transition-colors"
                                 onclick="selectStore('${store.identifier}', '${store.name.replace(/'/g, "\\'")}')">
                                <div class="font-medium text-gray-800 flex items-center gap-2">
                                    <span class="text-green-500">🏪</span>
                                    ${store.name}
                                </div>
                                <div class="text-xs text-gray-500 ml-6">${store.identifier}</div>
                            </div>
                        `;
                    });

                    if (listDiv) {
                        listDiv.innerHTML = html;
                    }
                } else {
                    if (listDiv) {
                        listDiv.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm text-center">검색 결과가 없습니다.</div>';
                    }
                }
            } catch (error) {
                console.error('Store search error:', error);
                const listDiv = document.getElementById('storeSearchResultsList');
                if (listDiv) {
                    listDiv.innerHTML = '<div class="px-3 py-2 text-red-500 text-sm text-center">검색 중 오류가 발생했습니다.</div>';
                }
            }
        }

        // Store search input event
        document.getElementById('storeSearchInput').addEventListener('input', (e) => {
            const keyword = e.target.value.trim();

            clearTimeout(searchTimeout);

            if (keyword.length >= 2) {
                searchTimeout = setTimeout(() => {
                    searchStores(keyword);
                }, 100); // 검색 대기 0.1초 지연
            } else {
                const listDiv = document.getElementById('storeSearchResultsList');
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">💡 검색 팁</div>
                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
            }
        });

        // Find Documents - Store search functionality
        function clearFindSelectedStore() {
            findSelectedStoreId = null;
            document.getElementById('findSelectedStoreInfo').classList.add('hidden');
        }

        function selectFindStore(storeId, storeName) {
            findSelectedStoreId = storeId;
            document.getElementById('findSelectedStoreName').textContent = storeName;
            document.getElementById('findSelectedStoreId').textContent = `ID: ${storeId}`;
            document.getElementById('findSelectedStoreInfo').classList.remove('hidden');

            // 검색 결과를 기본 상태로 되돌리기
            const listDiv = document.getElementById('findStoreSearchResultsList');
            if (listDiv) {
                listDiv.innerHTML = `
                    <div class="text-xs text-gray-500 text-center py-8">
                        <div class="mb-2">💡 검색 팁</div>
                        <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                        <div>• 최근 검색어가 자동으로 표시됩니다</div>
                    </div>
                `;
            }

            // 매장 선택 시 검색어 즉시 저장
            const searchInput = document.getElementById('findStoreSearchInput');
            const searchKeyword = searchInput.value.trim();
            if (searchKeyword.length >= 2) {
                saveSearchHistoryImmediately(searchKeyword);
            }

            searchInput.value = '';
        }

        async function searchFindStores(keyword) {
            const listDiv = document.getElementById('findStoreSearchResultsList');

            if (keyword.length < 2) {
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">💡 검색 팁</div>
                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
                return;
            }

            // 검색 기록 저장
            saveSearchHistory(keyword);

            try {
                const result = await makeApiCall(`/cdd/stores/search?keyword=${encodeURIComponent(keyword)}`);
                const resultsDiv = document.getElementById('findStoreSearchResults');

                if (result.success && result.stores.length > 0) {
                    const listDiv = document.getElementById('findStoreSearchResultsList');
                    let html = '';

                    result.stores.forEach(store => {
                        html += `
                            <div class="px-3 py-2 hover:bg-white/80 cursor-pointer rounded border border-gray-200/50 transition-colors"
                                 onclick="selectFindStore('${store.identifier}', '${store.name.replace(/'/g, "\\'")}')">
                                <div class="font-medium text-gray-800 flex items-center gap-2">
                                    <span class="text-blue-500">🏪</span>
                                    ${store.name}
                                </div>
                                <div class="text-xs text-gray-500 ml-6">${store.identifier}</div>
                            </div>
                        `;
                    });

                    if (listDiv) {
                        listDiv.innerHTML = html;
                    }
                } else {
                    if (listDiv) {
                        listDiv.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm text-center">검색 결과가 없습니다.</div>';
                    }
                }
            } catch (error) {
                console.error('Find store search error:', error);
                const listDiv = document.getElementById('findStoreSearchResultsList');
                if (listDiv) {
                    listDiv.innerHTML = '<div class="px-3 py-2 text-red-500 text-sm text-center">검색 중 오류가 발생했습니다.</div>';
                }
            }
        }

        // Find store search input events
        document.getElementById('findStoreSearchInput').addEventListener('input', (e) => {
            const keyword = e.target.value.trim();

            clearTimeout(findSearchTimeout);

            if (keyword.length >= 2) {
                findSearchTimeout = setTimeout(() => {
                    searchFindStores(keyword);
                }, 100);
            } else {
                const listDiv = document.getElementById('findStoreSearchResultsList');
                if (listDiv) {
                    listDiv.innerHTML = `
                        <div class="text-xs text-gray-500 text-center py-8">
                            <div class="mb-2">💡 검색 팁</div>
                            <div>• 매장명 또는 매장 ID를 2글자 이상 입력</div>
                            <div>• 최근 검색어가 자동으로 표시됩니다</div>
                        </div>
                    `;
                }
            }
        });

        document.getElementById('storeSearchInput').addEventListener('focus', (e) => {
            const keyword = e.target.value.trim();
            if (keyword.length < 2) {
                showSearchSuggestions('storeSearchInput', 'storeSearchResults');
            }
        });

        // Find store search input events (중복 제거됨)

        document.getElementById('findStoreSearchInput').addEventListener('focus', (e) => {
            const keyword = e.target.value.trim();
            if (keyword.length < 2) {
                showSearchSuggestions('findStoreSearchInput', 'findStoreSearchResults');
            }
        });

        // 클릭 외부 영역 처리 제거 (영역이 항상 표시되므로 불필요)

        // 페이지 로드 시 추천 검색어 미리 표시
        function initializeSuggestions() {
            // 매장 검색 기록 표시
            showSearchSuggestions('storeSearchInput', 'storeSearchResults');
            showSearchSuggestions('findStoreSearchInput', 'findStoreSearchResults');

            // Document ID 기록 표시
            showDocumentSuggestions('expireDocumentId', 'expireDocumentSuggestions');
            showDocumentSuggestions('modifyDocumentId', 'modifyDocumentSuggestions');
        }

        // 페이지 로드 완료 시 추천 검색어 초기화
        document.addEventListener('DOMContentLoaded', function() {
            initializeSuggestions();
        });

        // 1. Create CDD Document
        document.getElementById('createDocumentForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!selectedStoreId) {
                alert('매장을 선택해주세요.');
                return;
            }

            showLoading();
            try {
                const result = await makeApiCall(`/cdd/stores/${selectedStoreId}/create-document`, 'POST');
                showResult('createResult', result, result.success !== false);
            } catch (error) {
                showResult('createResult', `오류 발생: ${error.message}`, false);
            } finally {
                hideLoading();
            }
        });

        // 2. Find Documents
        document.getElementById('findDocumentsForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!findSelectedStoreId) {
                alert('매장을 선택해주세요.');
                return;
            }

            const params = new URLSearchParams();
            params.append('storeId', findSelectedStoreId);

            showLoading();
            try {
                const result = await makeApiCall(`/cdd/stores/documents?${params.toString()}`);
                showResult('findResult', result, result.success !== false);
            } catch (error) {
                showResult('findResult', `오류 발생: ${error.message}`, false);
            } finally {
                hideLoading();
            }
        });

        // 3. Expire Document
        document.getElementById('expireDocumentForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const documentId = document.getElementById('expireDocumentId').value.trim();
            if (!documentId) {
                alert('Document ID를 입력해주세요.');
                return;
            }

            // Document ID 검색 기록 저장
            saveDocumentHistory(documentId);

            showLoading();
            try {
                const result = await makeApiCall(`/cdd/documents/${documentId}/expire`, 'POST');
                showResult('expireResult', result, result.success !== false);
            } catch (error) {
                showResult('expireResult', `오류 발생: ${error.message}`, false);
            } finally {
                hideLoading();
            }
        });

        // 4. Modify Due Date
        document.getElementById('modifyDueDateForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const documentId = document.getElementById('modifyDocumentId').value.trim();
            const dueDate = document.getElementById('modifyDueDate').value;

            if (!documentId) {
                alert('Document ID를 입력해주세요.');
                return;
            }

            if (!dueDate) {
                alert('새로운 만료일을 선택해주세요.');
                return;
            }

            // Document ID 검색 기록 저장
            saveDocumentHistory(documentId);

            const params = new URLSearchParams();
            params.append('dueDate', dueDate);

            showLoading();
            try {
                const result = await makeApiCall(`/cdd/documents/${documentId}/modify-due-date?${params.toString()}`, 'POST');
                showResult('modifyResult', result, result.success !== false);
            } catch (error) {
                showResult('modifyResult', `오류 발생: ${error.message}`, false);
            } finally {
                hideLoading();
            }
        });

        // Document ID input focus events for suggestions
        document.getElementById('expireDocumentId').addEventListener('focus', (e) => {
            const value = e.target.value.trim();
            if (value.length < 10) {
                showDocumentSuggestions('expireDocumentId', 'expireDocumentSuggestions');
            }
        });

        document.getElementById('modifyDocumentId').addEventListener('focus', (e) => {
            const value = e.target.value.trim();
            if (value.length < 10) {
                showDocumentSuggestions('modifyDocumentId', 'modifyDocumentSuggestions');
            }
        });

        // Load environment information
        async function loadEnvironmentInfo() {
            try {
                const result = await makeApiCall('/cdd/environment');
                if (result.success && result.environment) {
                    const env = result.environment;

                    // 환경 배지 업데이트
                    const badge = document.getElementById('environmentBadge');
                    badge.className = `px-3 py-1 rounded-full text-sm font-medium ${env.color}`;

                    // 아이콘과 텍스트 업데이트
                    document.getElementById('environmentIcon').textContent = env.icon + ' ';
                    document.getElementById('environmentText').textContent = env.name;
                    document.getElementById('environmentDescription').textContent = env.description;
                } else {
                    // 실패 시 기본값 표시
                    document.getElementById('environmentBadge').className = 'px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800';
                    document.getElementById('environmentIcon').textContent = '❓ ';
                    document.getElementById('environmentText').textContent = '알 수 없음';
                    document.getElementById('environmentDescription').textContent = 'Unknown';
                }
            } catch (error) {
                console.error('Failed to load environment info:', error);
                // 에러 시 기본값 표시
                document.getElementById('environmentBadge').className = 'px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800';
                document.getElementById('environmentIcon').textContent = '❌ ';
                document.getElementById('environmentText').textContent = '로드 실패';
                document.getElementById('environmentDescription').textContent = 'Load Failed';
            }
        }

        // Delete All Documents Modal Functions
        function showDeleteAllModal() {
            console.log('showDeleteAllModal 호출됨, findSelectedStoreId:', findSelectedStoreId);

            if (!findSelectedStoreId) {
                alert('매장을 먼저 선택해주세요.');
                return;
            }

            // 모달에 매장 정보 설정
            const storeName = document.getElementById('findSelectedStoreName').textContent;
            document.getElementById('modalStoreName').textContent = storeName;
            document.getElementById('modalStoreId').textContent = findSelectedStoreId;
            document.getElementById('modalDocumentCount').textContent = '조회 중...';

            console.log('모달 정보 설정 완료 - 매장명:', storeName, '매장ID:', findSelectedStoreId);

            // 모달 표시
            document.getElementById('deleteAllModal').classList.remove('hidden');

            // 문서 개수 조회
            getDocumentCount(findSelectedStoreId);
        }

        function closeDeleteAllModal() {
            document.getElementById('deleteAllModal').classList.add('hidden');
        }

        async function getDocumentCount(storeId) {
            try {
                console.log('문서 개수 조회 시작:', storeId);
                const result = await makeApiCall(`/cdd/stores/${storeId}/documents/count`);
                console.log('문서 개수 조회 결과:', result);

                if (result.success) {
                    const count = result.document_count || 0;
                    document.getElementById('modalDocumentCount').textContent = `${count}개`;
                    console.log('문서 개수 설정 완료:', count);
                } else {
                    console.error('문서 개수 조회 실패:', result.message);
                    document.getElementById('modalDocumentCount').textContent = '조회 실패';
                }
            } catch (error) {
                console.error('문서 개수 조회 오류:', error);
                document.getElementById('modalDocumentCount').textContent = '조회 실패';
            }
        }

        function confirmDeleteAll() {
            const storeId = findSelectedStoreId;
            const storeName = document.getElementById('findSelectedStoreName').textContent;
            const documentCount = document.getElementById('modalDocumentCount').textContent;

            if (confirm(`정말로 "${storeName}" 매장의 모든 CDD 문서(${documentCount})를 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.`)) {
                deleteAllDocuments(storeId);
            }
        }

        async function deleteAllDocuments(storeId) {
            showLoading();
            closeDeleteAllModal();

            try {
                const result = await makeApiCall(`/cdd/stores/${storeId}/documents/delete-all`, 'POST');

                if (result.success) {
                    alert(`✅ ${result.message}\n삭제된 문서 수: ${result.documents_deleted}개`);

                    // 검색 결과 초기화
                    const findResultDiv = document.getElementById('findResult');
                    if (findResultDiv) {
                        findResultDiv.classList.add('hidden');
                    }
                } else {
                    alert(`❌ 삭제 실패: ${result.message}`);
                }
            } catch (error) {
                console.error('문서 삭제 실패:', error);
                alert(`❌ 문서 삭제 중 오류가 발생했습니다: ${error.message}`);
            } finally {
                hideLoading();
            }
        }

        // Initialize date input with today's date and load environment info
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('modifyDueDate').value = today;

            // 환경 정보 로드
            loadEnvironmentInfo();
        });
    </script>
</body>
</html>
