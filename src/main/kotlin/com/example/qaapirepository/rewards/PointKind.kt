package com.example.qaapirepository.rewards

enum class PointKind(val value: Int) {
    STORE_POINT(1),
    STORE_STAMP(2),
    ;

    companion object {
        fun valueOf(value: Int?): PointKind {
            return PointKind.values().find { it.value == value }
                ?: throw IllegalArgumentException("Point kind value is not valid. $value")
        }

        // TODO: 2022/07/18 point 공통 error code merge 되면 수정
        fun validatedValueOf(value: Int?): PointKind {
            return PointKind.values().find { it.value == value }
                ?: throw java.lang.IllegalArgumentException("point kind validation exception")
        }

        val STORE_POINT_STAMP: List<PointKind>
            get() = listOf(STORE_POINT, STORE_STAMP)
    }
}
