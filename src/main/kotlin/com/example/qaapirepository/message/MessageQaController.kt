package com.example.qaapirepository.message

import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.client.RestTemplate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID

@RestController
class MessageQaController(
    private val dbConnection: DBConnection,
    private val restTemplate: RestTemplate,
    private val jacksonObjectMapper: ObjectMapper,
    private val environment: Environment,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    private val messageServiceConnection = dbConnection.messageServiceConnection

    @Value("\${external-api.reward-service}")
    lateinit var rewardServiceUrl: String

    @GetMapping("/message/environment")
    fun getEnvironmentInfo(): ResponseEntity<Any> {
        val activeProfiles = environment.activeProfiles
        val profile = if (activeProfiles.isNotEmpty()) activeProfiles[0] else "default"

        val environmentInfo = when (profile.lowercase()) {
            "dev", "development" -> mapOf(
                "profile" to profile,
                "name" to "개발환경",
                "description" to "Development",
                "color" to "bg-green-100 text-green-800",
                "icon" to "🔧"
            )
            "stage", "staging" -> mapOf(
                "profile" to profile,
                "name" to "스테이지",
                "description" to "Staging",
                "color" to "bg-yellow-100 text-yellow-800",
                "icon" to "🚀"
            )
            "prod", "production" -> mapOf(
                "profile" to profile,
                "name" to "운영환경",
                "description" to "Production",
                "color" to "bg-red-100 text-red-800",
                "icon" to "⚡"
            )
            else -> mapOf(
                "profile" to profile,
                "name" to "기본환경",
                "description" to "Default",
                "color" to "bg-gray-100 text-gray-800",
                "icon" to "⚙️"
            )
        }

        return ResponseEntity.ok(
            mapOf(
                "success" to true,
                "environment" to environmentInfo
            )
        )
    }

    @GetMapping("/message/scheduled-messages/search")
    fun searchScheduledMessages(
        @RequestParam messageCode: String?,
        @RequestParam messageGroupName: String?,
        @RequestParam messageIdentifier: String?,
        @RequestParam limit: Int = 50
    ): ResponseEntity<Any> {
        log.info("Searching scheduled messages - messageCode: $messageCode, messageGroupName: $messageGroupName, messageIdentifier: $messageIdentifier")

        try {
            val queryBuilder = NativeQueryBuilder()
                .select(
                    "identifier",
                    "message_code",
                    "scheduled_delivery_date",
                    "message_group_identifier",
                    "message_group_name",
                    "message_identifier",
                    "message_name",
                    "message_kind",
                    "send_target",
                    "created_date",
                    "modified_date",
                    "scheduled_message_key",
                    "message_requests"
                )
                .from("scheduled_message")
                .where("1 = 1")

            if (!messageCode.isNullOrBlank()) {
                queryBuilder.and("message_code LIKE :messageCode")
                    .addParams("messageCode" to "%$messageCode%")
            }

            if (!messageGroupName.isNullOrBlank()) {
                queryBuilder.and("message_group_name LIKE :messageGroupName")
                    .addParams("messageGroupName" to "%$messageGroupName%")
            }

            if (!messageIdentifier.isNullOrBlank()) {
                val isUuidFormat = messageIdentifier.matches(
                    Regex("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")
                )
                if (isUuidFormat) {
                    queryBuilder.and("message_identifier = :messageIdentifier")
                        .addParams("messageIdentifier" to UUID.fromString(messageIdentifier))
                } else {
                    queryBuilder.and("message_name LIKE :messageName")
                        .addParams("messageName" to "%$messageIdentifier%")
                }
            }

            queryBuilder
                .orderBy("scheduled_delivery_date DESC, created_date DESC")
                .limit(limit)

            val results = messageServiceConnection.queryForList(queryBuilder.query, queryBuilder.queryParams)

            val messages = results.map { row ->
                val messageRequestsRaw = row["message_requests"]
                log.debug("Raw message_requests: $messageRequestsRaw, type: ${messageRequestsRaw?.javaClass}")

                val messageRequestsJson = when {
                    messageRequestsRaw is String -> messageRequestsRaw
                    messageRequestsRaw != null && messageRequestsRaw.javaClass.simpleName == "PGobject" -> {
                        // PGobject의 value 속성에 접근
                        try {
                            val valueMethod = messageRequestsRaw.javaClass.getMethod("getValue")
                            valueMethod.invoke(messageRequestsRaw) as? String
                        } catch (e: Exception) {
                            log.warn("Failed to get value from PGobject", e)
                            messageRequestsRaw.toString()
                        }
                    }
                    else -> messageRequestsRaw?.toString()
                }

                val parsedRequests = try {
                    if (!messageRequestsJson.isNullOrBlank()) {
                        jacksonObjectMapper.readValue(messageRequestsJson, List::class.java)
                    } else null
                } catch (e: Exception) {
                    log.warn("Failed to parse message_requests JSON: $messageRequestsJson", e)
                    null
                }

                // message_requests가 배열인 경우를 고려
                val firstRequest = if (parsedRequests is List<*> && (parsedRequests as List<*>).isNotEmpty()) {
                    parsedRequests[0] as? Map<*, *>
                } else {
                    parsedRequests as? Map<*, *>
                }

                val userIdentifier = firstRequest?.get("user_identifier")?.toString()
                val nonUserIdentifier = firstRequest?.get("non_user_identifier")?.toString()
                val visitorIdentifier = firstRequest?.get("visitor_identifier")?.toString()
                val phone = firstRequest?.get("phone")?.toString()

                log.info("Message ${row["identifier"]} - Raw: $messageRequestsJson")
                log.info("Message ${row["identifier"]} - Parsed: $parsedRequests")
                log.info("Message ${row["identifier"]} - FirstRequest: $firstRequest")
                log.info("Message ${row["identifier"]} - User info: userIdentifier=$userIdentifier, nonUserIdentifier=$nonUserIdentifier, visitorIdentifier=$visitorIdentifier, phone=$phone")

                mapOf(
                    "identifier" to row["identifier"],
                    "message_code" to row["message_code"],
                    "scheduled_delivery_date" to row["scheduled_delivery_date"],
                    "message_group_identifier" to row["message_group_identifier"],
                    "message_group_name" to row["message_group_name"],
                    "message_identifier" to row["message_identifier"],
                    "message_name" to row["message_name"],
                    "message_kind" to row["message_kind"],
                    "send_target" to row["send_target"],
                    "created_date" to row["created_date"],
                    "modified_date" to row["modified_date"],
                    "scheduled_message_key" to row["scheduled_message_key"],
                    "user_identifier" to userIdentifier,
                    "non_user_identifier" to nonUserIdentifier,
                    "visitor_identifier" to visitorIdentifier,
                    "phone" to phone,
                    "message_requests" to parsedRequests
                )
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "예약 메시지 조회 완료",
                    "total_count" to results.size,
                    "messages" to messages
                )
            )

        } catch (e: Exception) {
            log.error("Failed to search scheduled messages", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "예약 메시지 조회 중 오류가 발생했습니다.",
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @PostMapping("/message/scheduled-messages/{messageId}/send-immediately")
    @Transactional
    fun sendMessageImmediately(
        @PathVariable messageId: UUID
    ): ResponseEntity<Any> {
        log.info("Sending message immediately - messageId: $messageId")

        try {
            val currentTime = OffsetDateTime.now(ZoneOffset.UTC)

            val updateQuery = NativeQueryBuilder()
                .update("scheduled_message")
                .set("scheduled_delivery_date = :currentTime")
                .set("modified_date = :currentTime")
                .where("identifier = :messageId")
                .addParams("currentTime" to currentTime, "messageId" to messageId)

            val updatedRows = messageServiceConnection.update(updateQuery.query, updateQuery.queryParams)

            if (updatedRows == 0) {
                return ResponseEntity.notFound().build()
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "메시지가 즉시 발송으로 변경되었습니다.",
                    "message_id" to messageId,
                    "updated_delivery_date" to currentTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                )
            )

        } catch (e: Exception) {
            log.error("Failed to send message immediately - messageId: $messageId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "즉시 발송 변경 중 오류가 발생했습니다.",
                    "message_id" to messageId,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @PostMapping("/message/scheduled-messages/{messageId}/update-delivery-date")
    @Transactional
    fun updateDeliveryDate(
        @PathVariable messageId: UUID,
        @RequestParam newDeliveryDate: String
    ): ResponseEntity<Any> {
        log.info("Updating delivery date - messageId: $messageId, newDeliveryDate: $newDeliveryDate")

        try {
            val parsedDate = OffsetDateTime.parse(newDeliveryDate + "T00:00:00+09:00")
            val currentTime = OffsetDateTime.now(ZoneOffset.UTC)

            val updateQuery = NativeQueryBuilder()
                .update("scheduled_message")
                .set("scheduled_delivery_date = :newDeliveryDate")
                .set("modified_date = :currentTime")
                .where("identifier = :messageId")
                .addParams("newDeliveryDate" to parsedDate, "currentTime" to currentTime, "messageId" to messageId)

            val updatedRows = messageServiceConnection.update(updateQuery.query, updateQuery.queryParams)

            if (updatedRows == 0) {
                return ResponseEntity.notFound().build()
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "메시지 예약 시간이 변경되었습니다.",
                    "message_id" to messageId,
                    "new_delivery_date" to parsedDate.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                )
            )

        } catch (e: Exception) {
            log.error("Failed to update delivery date - messageId: $messageId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "예약 시간 변경 중 오류가 발생했습니다.",
                    "message_id" to messageId,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @PostMapping("/message/scheduled-messages/{messageId}/update-delivery-datetime")
    @Transactional
    fun updateDeliveryDateTime(
        @PathVariable messageId: UUID,
        @RequestParam newDeliveryDateTime: String
    ): ResponseEntity<Any> {
        log.info("Updating delivery datetime - messageId: $messageId, newDeliveryDateTime: $newDeliveryDateTime")

        try {
            val parsedDateTime = OffsetDateTime.parse(newDeliveryDateTime)
            val currentTime = OffsetDateTime.now(ZoneOffset.UTC)

            val updateQuery = NativeQueryBuilder()
                .update("scheduled_message")
                .set("scheduled_delivery_date = :newDeliveryDateTime")
                .set("modified_date = :currentTime")
                .where("identifier = :messageId")
                .addParams("newDeliveryDateTime" to parsedDateTime, "currentTime" to currentTime, "messageId" to messageId)

            val updatedRows = messageServiceConnection.update(updateQuery.query, updateQuery.queryParams)

            if (updatedRows == 0) {
                return ResponseEntity.notFound().build()
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "메시지 예약 시간이 변경되었습니다.",
                    "message_id" to messageId,
                    "new_delivery_date" to parsedDateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                )
            )

        } catch (e: Exception) {
            log.error("Failed to update delivery datetime - messageId: $messageId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "예약 시간 변경 중 오류가 발생했습니다.",
                    "message_id" to messageId,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @PostMapping("/message/scheduled-messages/bulk-send-immediately")
    @Transactional
    fun bulkSendImmediately(
        @RequestParam messageIds: List<UUID>
    ): ResponseEntity<Any> {
        log.info("Bulk sending messages immediately - messageIds: $messageIds")

        try {
            val currentTime = OffsetDateTime.now(ZoneOffset.UTC)

            val updateQuery = NativeQueryBuilder()
                .update("scheduled_message")
                .set("scheduled_delivery_date = :currentTime")
                .set("modified_date = :currentTime")
                .where("identifier IN (:messageIds)")
                .addParams("currentTime" to currentTime, "messageIds" to messageIds)

            val updatedRows = messageServiceConnection.update(updateQuery.query, updateQuery.queryParams)

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "${updatedRows}개의 메시지가 즉시 발송으로 변경되었습니다.",
                    "updated_count" to updatedRows,
                    "message_ids" to messageIds,
                    "updated_delivery_date" to currentTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
                )
            )

        } catch (e: Exception) {
            log.error("Failed to bulk send messages immediately - messageIds: $messageIds", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "일괄 즉시 발송 변경 중 오류가 발생했습니다.",
                    "message_ids" to messageIds,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }
}
