spring.application.name: qa-api-repository
server.port: 9981

# default datasource

spring.datasource.url: *****************************************
spring.datasource.username: root
spring.datasource.password: passorder
spring.datasource.driver-class-name: org.postgresql.Driver

# JPA
spring.jpa.database-platform: org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.dialect: org.hibernate.dialect.PostgreSQLDialect


monolith.datasource:
  pool-name: monolith_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

event-service.datasource:
  pool-name: event_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

settlement-service.datasource:
  pool-name: settlement_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

analysis-service.datasource:
  pool-name: analysis_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

reward-service.datasource:
  pool-name: reward_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

auth-service.datasource:
  pool-name: auth_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

passmoney-ledger-service.datasource:
  pool-name: passmoney_ledger_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

settlement-ledger-service.datasource:
  pool-name: settlement_ledger_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

message-service.datasource:
  pool-name: message_service_db
  username: passorder
  password: Q2tMbnZXRHk1YXRlWkpjcVhpZDFyYTZzaE5FeTRo
  jdbc-url: ******************************************

external-api:
  reward-service: "http://localhost:8080/points"
  store-service: "http://localhost:8080/stores"
  monolith: "http://localhost:8080"

server:
  tomcat:
    additional-tld-skip-patterns: "*.ico"
    accesslog:
      enabled: true
      directory: .
      prefix: access_log
      suffix: .log
      pattern: '%h %l %u %t "%r" %s %b'
      file-date-format: yyyy-MM-dd
      rotate: false
