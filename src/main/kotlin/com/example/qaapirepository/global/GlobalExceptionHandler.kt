package com.example.qaapirepository.global

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice


@RestControllerAdvice
class GlobalExceptionHandler() {

    private val log = LoggerFactory.getLogger(javaClass)

    /**
     * 서버 에러
     */
    @ExceptionHandler(value = [Exception::class])
    protected fun handleException(e: Exception): ResponseEntity<ErrorResponse> {
        log.error("handle none caught exception.", e)

        val response = ErrorResponse(code = "INTERNAL_SERVER_ERROR", message = e.message)
        return ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR)
    }
}
