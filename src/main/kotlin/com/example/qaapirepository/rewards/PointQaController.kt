package com.example.qaapirepository.rewards

import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import java.util.UUID

@RestController
class PointQaController(
    dbConnection: DBConnection,
    private val restTemplate: RestTemplate,
    private val jacksonObjectMapper: ObjectMapper,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    val rewardServiceConnection = dbConnection.rewardServiceConnection

    @Value("\${external-api.reward-service}")
    lateinit var rewardServiceUrl: String


    @GetMapping("/rewards/collecting-rewards/move-date")
    @Transactional
    fun movePointDate(
        @RequestParam userId: UUID,
        @RequestParam storeId: UUID?,
        @RequestParam("movedDays") movedDays: Int,
    ): String {
        val pointExecuteCount: Int = NativeQueryBuilder()
            .update("point")
            .set("date = date + interval '${movedDays} days'")
            .andSet("expiry_date = expiry_date + interval '${movedDays} days'")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .`if`(storeId != null) {
                and("store_identifier = :storeId").addParams("storeId" to storeId)
            }
            .and("kind in (:kinds)").addParams("kinds" to listOf(1, 2))
            .let {
                println(it.query)
                println(it.queryParams)
                rewardServiceConnection.update(it.query, it.queryParams)
            }

        val couponIdentifier: UUID? = if (storeId != null) {
            NativeQueryBuilder()
                .select("coupon_identifier")
                .from("store_collect_policy")
                .where("store_collect_policy.store_id = :storeId").addParams("storeId" to storeId)
                .let { rewardServiceConnection.queryForMap(it.query, it.queryParams) }
                .let { it["coupon_identifier"] as? UUID? }
        } else null

        val issuedCouponExecuteCount = NativeQueryBuilder()
            .update("issued_coupon")
            .set("created_date = created_date + interval '${movedDays} days'")
            .andSet("got_date = got_date + interval '${movedDays} days'")
            .andSet("expiry_date = expiry_date + interval '${movedDays} days'")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .`if`(couponIdentifier != null) {
                and("coupon_identifier = :couponIdentifier").addParams("couponIdentifier" to couponIdentifier)
            }
            .let { rewardServiceConnection.update(it.query, it.queryParams) }


        val salesPostIdentifierListResult = NativeQueryBuilder()
            .select("identifier")
            .from("sales_post")
            .where("sales_user_identifier = :userId").addParams("userId" to userId)
            .`if`(storeId != null) {
                and("store_identifier = :storeId").addParams("storeId" to storeId)
            }.let { rewardServiceConnection.queryForList(it.query, it.queryParams) }

        val salesPostIds = salesPostIdentifierListResult.mapNotNull { it["identifier"] as? UUID? }

        val salesPostExecuteCount = if (salesPostIds.isNotEmpty()) {
            NativeQueryBuilder()
                .update("sales_post")
                .set("created_date = created_date + interval '${movedDays} days'")
                .andSet("last_modified_date = last_modified_date + interval '${movedDays} days'")
                .andSet("sold_date = sold_date + interval '${movedDays} days'")
                .andSet("scheduled_sales_date = scheduled_sales_date + interval '${movedDays} days'")
                .where("identifier in (:salesPostIds)").addParams("salesPostIds" to salesPostIds)
                .let { rewardServiceConnection.update(it.query, it.queryParams) }
        } else 0


        val salesItemExecuteCount = if (salesPostIds.isNotEmpty()) {
            NativeQueryBuilder()
                .update("sales_post_sales_item")
                .set("created_date = created_date + interval '${movedDays} days'")
                .andSet("expiry_date = expiry_date + interval '${movedDays} days'")
                .where("sales_post_identifier in (:salesPostIds)").addParams("salesPostIds" to salesPostIds)
                .let { rewardServiceConnection.update(it.query, it.queryParams) }
        } else 0


        return """
            ====================================================
            Move point date success. 
            userId : ${userId}, storeId : ${storeId}, movedDays = ${movedDays}, 
            Moved point [${pointExecuteCount}] rows, 
            Moved issued coupon [${issuedCouponExecuteCount}] rows,
            Moved sales post [${salesPostExecuteCount}] rows,
            Moved sales item [${salesItemExecuteCount}] rows
            ====================================================
        """.trimIndent()
    }

    @GetMapping("/rewards/collecting-rewards/clear-all")
    @Transactional
    fun clearAllCollectingRewards(
        @RequestParam userId: UUID,
    ): String {
        val pointExecuteCount = NativeQueryBuilder()
            .delete("point")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .let { rewardServiceConnection.update(it.query, it.queryParams) }

        val issuedCouponExecuteCount = NativeQueryBuilder()
            .delete("issued_coupon")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .let { rewardServiceConnection.update(it.query, it.queryParams) }

        val salesPostExecuteCount = NativeQueryBuilder()
            .delete("sales_post")
            .where("sales_user_identifier = :userId").addParams("userId" to userId)
            .let { rewardServiceConnection.update(it.query, it.queryParams) }

        return """
            Clear all collecting rewards success. userId : ${userId}, Cleared point [${pointExecuteCount}] rows, Cleared issued coupon [${issuedCouponExecuteCount}] rows, Cleared sales post [${salesPostExecuteCount}] rows
        """.trimIndent()
    }

    @GetMapping("/rewards/collecting-rewards/clear-by-user-and-store")
    @Transactional
    fun clearCollectingRewardsByUserAndStore(
        @RequestParam userId: UUID,
        @RequestParam storeId: UUID,
    ): String {
        log.info("Clearing collecting rewards for userId: $userId, storeId: $storeId")

        // 포인트 삭제 (특정 회원, 특정 매장)
        val pointExecuteCount = NativeQueryBuilder()
            .delete("point")
            .where("user_identifier = :userId").addParams("userId" to userId)
            .and("store_identifier = :storeId").addParams("storeId" to storeId)
            .let { rewardServiceConnection.update(it.query, it.queryParams) }

        // 해당 매장의 쿠폰 식별자 조회
        val couponIdentifier: UUID? = NativeQueryBuilder()
            .select("coupon_identifier")
            .from("store_collect_policy")
            .where("store_id = :storeId").addParams("storeId" to storeId)
            .let {
                val result = rewardServiceConnection.queryForList(it.query, it.queryParams)
                result.firstOrNull()?.get("coupon_identifier") as? UUID?
            }

        // 발급된 쿠폰 삭제 (특정 회원, 특정 매장의 쿠폰)
        val issuedCouponExecuteCount = if (couponIdentifier != null) {
            NativeQueryBuilder()
                .delete("issued_coupon")
                .where("user_identifier = :userId").addParams("userId" to userId)
                .and("coupon_identifier = :couponIdentifier").addParams("couponIdentifier" to couponIdentifier)
                .let { rewardServiceConnection.update(it.query, it.queryParams) }
        } else {
            log.warn("No coupon identifier found for storeId: $storeId")
            0
        }

        // 판매글 삭제 (특정 회원, 특정 매장)
        val salesPostExecuteCount = NativeQueryBuilder()
            .delete("sales_post")
            .where("sales_user_identifier = :userId").addParams("userId" to userId)
            .and("store_identifier = :storeId").addParams("storeId" to storeId)
            .let { rewardServiceConnection.update(it.query, it.queryParams) }

        return """
            ====================================================
            Clear collecting rewards by user and store success.
            userId: $userId, storeId: $storeId
            Cleared point [$pointExecuteCount] rows
            Cleared issued coupon [$issuedCouponExecuteCount] rows
            Cleared sales post [$salesPostExecuteCount] rows
            ====================================================
        """.trimIndent()
    }

    @GetMapping("/rewards/collecting-stores/search")
    fun searchCollectingStores(
        @RequestParam keyword: String,
    ): String {
        log.info("searchCollectingStores keyword : $keyword")

        if (keyword.isBlank() || keyword.length < 2) {
            return "keyword is blank. result not search"
        }

        val results = NativeQueryBuilder()
            .select(
                "store_id",
                "store_name",
                "point_kind",
                "coupon_identifier",
                "coupon_conversion_strategy",
                "coupon_benefit"
            )
            .from("store_collect_policy")
            .where("store_name like :keyword")
            .addParams("keyword" to "%${keyword}%")
            .let {
                rewardServiceConnection.queryForList(it.query, it.queryParams)
            }

        val builder = StringBuilder()

        results.forEach {
            builder.appendLine(it)
        }

        return """
            ========================
            $builder
            ========================
        """.trimIndent()
    }

    @GetMapping("/rewards/scheduled-sales-posts/update-to-selling")
    fun updateScheduledSalesPostsToSelling(
        @RequestParam("targetDate") targetDate: String?,
    ): String {
        log.info("Update scheduled sales posts to selling. targetDate : $targetDate")

        val result: String? = restTemplate.getForObject(
            UriComponentsBuilder
                .fromHttpUrl("${rewardServiceUrl}/collecting-market/qa/activate-sales-scheduled-sales-post")
                .queryParam("targetDate", targetDate)
                .build()
                .toUri(),
            String::class.java
        )

        return """
            Update scheduled sales posts to selling success. targetDate : $targetDate, result : $result
        """.trimIndent()
    }

    @GetMapping("/rewards/collecting-rewards/offline-collecting")
    fun accumulateOfflineCollecting(
        @RequestParam userId: UUID,
        @RequestParam storeId: UUID,
        @RequestParam point: Int,
        @RequestParam pointKind: PointKind,
    ): String {

        log.info("Offline collecting. userId : $userId, storeId : $storeId, point : $point, pointKind : $pointKind")


        val result: String? = restTemplate.postForObject(
            "${rewardServiceUrl}/stores/${storeId}/users/${userId}",
            mapOf(
                "point" to point,
                "point_kind" to pointKind.value
            ),
            String::class.java
        )

        return """
            ===============================================
            Offline collecting success. userId: $userId, storeId: $storeId, point: $point, pointKind: $pointKind, result: $result
            ===============================================
        """.trimIndent()
    }
}
