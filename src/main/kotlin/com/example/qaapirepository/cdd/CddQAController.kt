package com.example.qaapirepository.cdd

import com.example.qaapirepository.auth.AuthenticationService
import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.client.RestTemplate
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID

@RestController
class CddQAController(
    private val dbConnection: DBConnection,
    private val restTemplate: RestTemplate,
    private val jacksonObjectMapper: ObjectMapper,
    private val environment: Environment,
    private val authenticationService: AuthenticationService,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    private val monolithServiceConnection = dbConnection.monolithServiceConnection

    @Value("\${external-api.store-service}")
    lateinit var storeServiceUrl: String

    @GetMapping("/cdd/environment")
    fun getEnvironmentInfo(): ResponseEntity<Any> {
        val activeProfiles = environment.activeProfiles
        val profile = if (activeProfiles.isNotEmpty()) activeProfiles[0] else "default"

        val environmentInfo = when (profile.lowercase()) {
            "dev", "development" -> mapOf(
                "profile" to profile,
                "name" to "개발환경",
                "description" to "Development",
                "color" to "bg-green-100 text-green-800",
                "icon" to "🔧"
            )
            "stage", "staging" -> mapOf(
                "profile" to profile,
                "name" to "스테이지",
                "description" to "Staging",
                "color" to "bg-yellow-100 text-yellow-800",
                "icon" to "🚀"
            )
            "prod", "production" -> mapOf(
                "profile" to profile,
                "name" to "운영환경",
                "description" to "Production",
                "color" to "bg-red-100 text-red-800",
                "icon" to "🏭"
            )
            else -> mapOf(
                "profile" to profile,
                "name" to "기본환경",
                "description" to "Default",
                "color" to "bg-gray-100 text-gray-800",
                "icon" to "⚙️"
            )
        }

        return ResponseEntity.ok(
            mapOf(
                "success" to true,
                "environment" to environmentInfo
            )
        )
    }

    @GetMapping("/cdd/stores/search")
    fun searchStores(
        @RequestParam keyword: String,
    ): ResponseEntity<Any> {
        log.info("Searching stores with keyword: $keyword")

        if (keyword.isBlank() || keyword.length < 2) {
            return ResponseEntity.badRequest().body(
                mapOf(
                    "success" to false,
                    "message" to "검색어는 2글자 이상 입력해주세요."
                )
            )
        }

        try {
            val queryBuilder = NativeQueryBuilder()
                .select("identifier", "name")
                .from("store")
                .where("1 = 1")

            // UUID 형식인지 확인 (36자리, 하이픈 포함)
            val isUuidFormat =
                keyword.matches(Regex("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"))

            if (isUuidFormat) {
                // UUID 형식이면 식별자로 정확히 검색하거나 매장명으로 LIKE 검색
                queryBuilder.and("(identifier = :identifier OR name LIKE :keyword)")
                    .addParams("identifier" to UUID.fromString(keyword), "keyword" to "%$keyword%")
            } else {
                // UUID 형식이 아니면 매장명으로만 LIKE 검색
                queryBuilder.and("name LIKE :keyword")
                    .addParams("keyword" to "%$keyword%")
            }

            queryBuilder
                .orderBy("created_date DESC") // 생성일 기준 최신 순 정렬
                .limit(20) // 최대 20개까지만 검색

            val results = monolithServiceConnection.queryForList(queryBuilder.query, queryBuilder.queryParams)

            val stores = results.map { row ->
                mapOf(
                    "identifier" to row["identifier"],
                    "name" to row["name"]
                )
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "매장 검색 완료",
                    "stores" to stores,
                    "search_type" to if (isUuidFormat) "identifier_and_name" else "name_only"
                )
            )

        } catch (e: Exception) {
            log.error("Failed to search stores", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "매장 검색 중 오류가 발생했습니다.",
                    "error" to e.message
                )
            )
        }
    }

    @PostMapping("/cdd/stores/{storeId}/create-document")
    fun createCDDDocument(
        @PathVariable storeId: UUID,
    ): ResponseEntity<Any> {
        log.info("Creating CDD document for storeId: $storeId")

        try {
            val accessToken = authenticationService.loginWithGuestAuth()

            val url = "$storeServiceUrl/$storeId/customer-due-diligence-documents"

            val headers = HttpHeaders().apply {
                contentType = MediaType.APPLICATION_JSON
                setBearerAuth(accessToken)
            }

            val requestBody = mapOf("send_crm_notification" to false)
            val requestEntity = HttpEntity(requestBody, headers)

            val response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String::class.java
            )

            log.info("CDD document creation response: ${response.statusCode}")

            // 응답에서 문서 ID 추출
            var documentId: String? = null
            try {
                response.body?.let { responseBody ->
                    val responseJson = jacksonObjectMapper.readTree(responseBody)
                    documentId = responseJson.get("identifier")?.asText()
                }
            } catch (e: Exception) {
                log.warn("Failed to parse document ID from response", e)
            }

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "CDD 문서가 성공적으로 생성되었습니다.",
                    "store_id" to storeId,
                    "document_id" to documentId,
                    "api_response" to mapOf(
                        "status_code" to response.statusCode.value(),
                        "status_text" to response.statusCode.toString(),
                        "response_body" to response.body
                    ),
                    "request_info" to mapOf(
                        "api_url" to url,
                        "method" to "POST",
                        "send_crm_notification" to false
                    )
                )
            )

        } catch (e: Exception) {
            log.error("Failed to create CDD document for storeId: $storeId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "CDD 문서 생성 중 오류가 발생했습니다.",
                    "store_id" to storeId,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @GetMapping("/cdd/stores/documents")
    fun findDocumentsByStoreIdOrStoreName(
        @RequestParam storeId: UUID?,
        @RequestParam storeName: String?,
    ): ResponseEntity<Any> {
        log.info("Finding CDD documents by storeId: $storeId, storeName: $storeName")

        if (storeId == null && storeName.isNullOrBlank()) {
            return ResponseEntity.badRequest().body(
                mapOf(
                    "success" to false,
                    "message" to "Either storeId or storeName must be provided"
                )
            )
        }

        try {
            val queryBuilder = NativeQueryBuilder()
                .select(
                    "store.identifier as store_id",
                    "store.name as store_name",
                    "customer_due_diligence_document.identifier as document_id",
                    "customer_due_diligence_document.status as document_status",
                    "customer_due_diligence_document.created_date as document_created_date",
                    "customer_due_diligence_document.due_date as document_due_date"
                )
                .from("customer_due_diligence_document")
                .join("store on store.identifier = customer_due_diligence_document.store_identifier")
                .where("1 = 1")

            when {
                storeId != null && !storeName.isNullOrBlank() -> {
                    queryBuilder.and("(store.identifier = :storeId OR store.name LIKE :storeName)")
                        .addParams("storeId" to storeId, "storeName" to "%$storeName%")
                }

                storeId != null -> {
                    queryBuilder.and("store.identifier = :storeId")
                        .addParams("storeId" to storeId)
                }

                !storeName.isNullOrBlank() -> {
                    queryBuilder.and("store.name LIKE :storeName")
                        .addParams("storeName" to "%$storeName%")
                }
            }

            queryBuilder.orderBy("customer_due_diligence_document.created_date DESC") // 생성일 기준 최신 순 정렬

            val results = monolithServiceConnection.queryForList(queryBuilder.query, queryBuilder.queryParams)

            val documents = results.map { row ->
                mapOf(
                    "store_id" to row["store_id"],
                    "store_name" to row["store_name"],
                    "document_id" to row["document_id"],
                    "document_status" to row["document_status"],
                    "document_created_date" to row["document_created_date"],
                    "document_due_date" to row["document_due_date"]
                )
            }

            log.info("Found CDD documents: $documents")

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "CDD documents found successfully",
                    "total_count" to results.size,
                    "documents" to documents
                )
            )

        } catch (e: Exception) {
            log.error("Failed to find CDD documents", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Failed to find CDD documents",
                    "error" to e.message
                )
            )
        }
    }

    @PostMapping("/cdd/documents/{documentId}/expire")
    fun expireDocument(
        @PathVariable documentId: UUID,
    ): ResponseEntity<Any> {
        log.info("Expiring CDD document: $documentId")

        try {
            // 먼저 현재 문서 상태 조회
            val currentStatusQuery = NativeQueryBuilder()
                .select("status")
                .from("customer_due_diligence_document")
                .where("identifier = :documentId")
                .addParams("documentId" to documentId)

            val currentStatusData = monolithServiceConnection.queryForList(currentStatusQuery.query, currentStatusQuery.queryParams)

            if (currentStatusData.isEmpty()) {
                return ResponseEntity.notFound().build()
            }

            val currentStatus = currentStatusData[0]["status"] as String

            val accessToken = authenticationService.loginWithGuestAuth()

            val url = "$storeServiceUrl/customer-due-diligence-documents/$documentId"

            val headers = HttpHeaders().apply {
                contentType = MediaType.APPLICATION_JSON
                setBearerAuth(accessToken)
            }

            val requestBody = mapOf("status" to "EXPIRED")
            val requestEntity = HttpEntity(requestBody, headers)

            val response = restTemplate.exchange(
                url,
                HttpMethod.PUT,
                requestEntity,
                String::class.java
            )

            log.info("CDD document expiration response: ${response.statusCode}")

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "CDD 문서가 성공적으로 만료 처리되었습니다.",
                    "document_id" to documentId,
                    "status_change" to mapOf(
                        "from" to currentStatus,
                        "to" to "EXPIRED"
                    ),
                    "api_response" to mapOf(
                        "status_code" to response.statusCode.value(),
                        "status_text" to response.statusCode.toString(),
                        "response_body" to response.body
                    ),
                    "request_info" to mapOf(
                        "api_url" to url,
                        "method" to "PUT",
                        "new_status" to "EXPIRED"
                    )
                )
            )

        } catch (e: Exception) {
            log.error("Failed to expire CDD document: $documentId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "CDD 문서 만료 처리 중 오류가 발생했습니다.",
                    "document_id" to documentId,
                    "error" to e.message,
                    "error_type" to e.javaClass.simpleName
                )
            )
        }
    }

    @PostMapping("/cdd/documents/{documentId}/modify-due-date")
    fun modifyDocumentDueDate(
        @PathVariable documentId: UUID,
        @RequestParam dueDate: String,
    ): ResponseEntity<Any> {
        log.info("Modifying CDD document due date: $documentId to $dueDate")

        try {
            // 현재 due_date 조회
            val currentDueDateQuery = NativeQueryBuilder()
                .select("due_date", "created_date")
                .from("customer_due_diligence_document")
                .where("identifier = :documentId")
                .addParams("documentId" to documentId)

            val currentData =
                monolithServiceConnection.queryForList(currentDueDateQuery.query, currentDueDateQuery.queryParams)

            if (currentData.isEmpty()) {
                return ResponseEntity.notFound().build()
            }

            val currentDueDate = currentData[0]["due_date"] as java.sql.Timestamp
            val currentCreatedDate = currentData[0]["created_date"] as java.sql.Timestamp

            // 새로운 due_date 파싱 (yyyy-MM-dd 형식 가정)
            val newDueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_LOCAL_DATE)
            val currentDueDateLocal = currentDueDate.toLocalDateTime().toLocalDate()

            // 날짜 차이 계산
            val daysDifference = ChronoUnit.DAYS.between(currentDueDateLocal, newDueDate)

            // due_date와 created_date 업데이트
            val updateQuery = NativeQueryBuilder()
                .update("customer_due_diligence_document")
                .set("due_date = due_date + interval '$daysDifference days'")
                .andSet("created_date = created_date + interval '$daysDifference days'")
                .where("identifier = :documentId")
                .addParams("documentId" to documentId)

            val executeCount = monolithServiceConnection.update(updateQuery.query, updateQuery.queryParams)

            log.info("Updated CDD document due date. documentId: $documentId, daysDifference: $daysDifference, executeCount: $executeCount")

            // 변경 후 날짜 계산 - OffsetDateTime으로 한국 시간대 정보 포함
            val koreaZoneOffset = ZoneOffset.of("+09:00")

            val originalCreatedDateTime = currentCreatedDate.toLocalDateTime().atOffset(koreaZoneOffset)
            val originalDueDateTime = currentDueDate.toLocalDateTime().atOffset(koreaZoneOffset)
            val newCreatedDateTime = originalCreatedDateTime.plusDays(daysDifference)
            val newDueDateTime = originalDueDateTime.plusDays(daysDifference)

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "문서 만료일이 성공적으로 수정되었습니다.",
                    "document_id" to documentId,
                    "date_changes" to mapOf(
                        "original_created_date" to originalCreatedDateTime.toString(),
                        "original_due_date" to originalDueDateTime.toString(),
                        "new_created_date" to newCreatedDateTime.toString(),
                        "new_due_date" to newDueDateTime.toString()
                    ),
                    "days_moved" to daysDifference,
                    "rows_updated" to executeCount
                )
            )

        } catch (e: Exception) {
            log.error("Failed to modify CDD document due date: $documentId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "Failed to modify document due date",
                    "document_id" to documentId,
                    "error" to e.message
                )
            )
        }
    }

    @GetMapping("/cdd/stores/{storeId}/documents/count")
    fun getDocumentCountByStore(
        @PathVariable storeId: UUID
    ): ResponseEntity<Any> {
        log.info("Getting document count for store: $storeId")

        try {
            val countQuery = NativeQueryBuilder()
                .select("COUNT(*) as document_count")
                .from("customer_due_diligence_document")
                .where("store_identifier = :storeId")
                .addParams("storeId" to storeId)

            val result = monolithServiceConnection.queryForList(countQuery.query, countQuery.queryParams)
            val documentCount = (result[0]["document_count"] as Number).toInt()

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "store_id" to storeId,
                    "document_count" to documentCount
                )
            )

        } catch (e: Exception) {
            log.error("Failed to get document count for store: $storeId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "문서 개수 조회 중 오류가 발생했습니다.",
                    "store_id" to storeId,
                    "error" to e.message
                )
            )
        }
    }

    @PostMapping("/cdd/stores/{storeId}/documents/delete-all")
    fun deleteAllDocumentsByStore(
        @PathVariable storeId: UUID
    ): ResponseEntity<Any> {
        log.info("Deleting all CDD documents for store: $storeId")

        try {
            // 먼저 해당 매장의 문서 개수와 매장 정보 조회
            val storeInfoQuery = NativeQueryBuilder()
                .select("COUNT(*) as document_count")
                .from("customer_due_diligence_document")
                .where("store_identifier = :storeId")
                .addParams("storeId" to storeId)

            val documentCountResult = monolithServiceConnection.queryForList(storeInfoQuery.query, storeInfoQuery.queryParams)
            val documentCount = (documentCountResult[0]["document_count"] as Number).toInt()

            if (documentCount == 0) {
                return ResponseEntity.ok(
                    mapOf(
                        "success" to true,
                        "message" to "해당 매장에 삭제할 문서가 없습니다.",
                        "store_id" to storeId,
                        "documents_deleted" to 0
                    )
                )
            }

            // 모든 문서 삭제
            val deleteQuery = NativeQueryBuilder()
                .delete("customer_due_diligence_document")
                .where("store_identifier = :storeId")
                .addParams("storeId" to storeId)

            val deletedCount = monolithServiceConnection.update(deleteQuery.query, deleteQuery.queryParams)

            log.info("Deleted all CDD documents for store: $storeId, count: $deletedCount")

            return ResponseEntity.ok(
                mapOf(
                    "success" to true,
                    "message" to "매장의 모든 CDD 문서가 성공적으로 삭제되었습니다.",
                    "store_id" to storeId,
                    "documents_deleted" to deletedCount
                )
            )

        } catch (e: Exception) {
            log.error("Failed to delete all CDD documents for store: $storeId", e)
            return ResponseEntity.internalServerError().body(
                mapOf(
                    "success" to false,
                    "message" to "문서 삭제 중 오류가 발생했습니다.",
                    "store_id" to storeId,
                    "error" to e.message
                )
            )
        }
    }
}

