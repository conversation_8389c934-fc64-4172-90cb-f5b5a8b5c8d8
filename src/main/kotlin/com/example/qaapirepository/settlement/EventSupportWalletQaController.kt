package com.example.qaapirepository.settlement

import com.example.qaapirepository.dataosurce.DBConnection
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
class EventSupportWalletQaController(
    dbConnection: DBConnection
) {

    val settlementServiceConnection = dbConnection.settlementServiceConnection
    val settlementLedgerConnection = dbConnection.settlementLedgerConnection

    @GetMapping("/stores/{storeId}/event-support-fund-histories/move-date")
    @Transactional
    fun modifyEventSupportFundHistory(
        @PathVariable storeId: UUID,
        @RequestParam("movedDays") movedDays: Int,
    ): String {

        val query = """
            update event_support_fund_wallet_history
            set created_date = event_support_fund_wallet_history.created_date + interval '${movedDays} days'
            from event_support_fund_wallet
            where event_support_fund_wallet_history.event_support_fund_wallet_identifier = event_support_fund_wallet.identifier
              and event_support_fund_wallet.store_identifier = :storeId;
        """.trimIndent()

        val queryParams = mapOf(
            "storeId" to storeId,
        )

        val summaryQuery ="""
            update event_support_fund_wallet_daily_summary
            set occurred_date = occurred_date + interval '${movedDays} days'
                where store_identifier = :storeId;
        """.trimIndent()

        val executeCount = settlementServiceConnection.update(query, queryParams)
        val executeCount2= settlementLedgerConnection.update(query, queryParams)
        val summaryExecuteCount = settlementServiceConnection.update(summaryQuery, queryParams)

        return """
            Update success. storeId : ${storeId}, movedDays : ${movedDays}. 
            history moved count : ${executeCount}, summary moved count : $summaryExecuteCount
            ledger history moved count : ${executeCount2}
        """.trimIndent()
    }
}
