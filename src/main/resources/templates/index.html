<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>qa apis index page</title>
</head>
<body>

<h1>QA API Pages</h1>


<!-- link to collecting market qa page button -->
<button onclick="window.location.href = '/collecting-market-qa';">GO to Collecting Market QA Page</button>

<br><br>

<!-- link to customer due diligence qa page button -->
<button onclick="window.location.href = '/customer-due-diligence-qa';">GO to Customer Due Diligence QA Page</button>

<br><br>

<!-- link to message qa page button -->
<button onclick="window.location.href = '/message-qa';">GO to Message QA Page</button>

<br><br>

<!-- link to admin page button -->
<button onclick="openAdminPage();">GO to Admin Page</button>

<script>
// Admin URL 설정 - ngrok 사용 시 여기를 수정하세요
const ADMIN_URL_CONFIG = {
    // ngrok 사용 시 Admin 페이지의 ngrok URL을 여기에 설정
    ngrok: '', // 예: 'https://admin-abc123.ngrok.io/admin/'

    // 로컬/일반 환경에서는 자동 감지
    auto: true
};

function openAdminPage() {
    const currentHost = window.location.hostname;
    const currentProtocol = window.location.protocol;

    let adminUrl;

    // ngrok 도메인 감지
    if (currentHost.includes('ngrok.io') || currentHost.includes('ngrok-free.app') || currentHost.includes('ngrok.app')) {
        if (ADMIN_URL_CONFIG.ngrok) {
            // 설정된 ngrok Admin URL 사용
            adminUrl = ADMIN_URL_CONFIG.ngrok;
        } else {
            // ngrok Admin URL이 설정되지 않은 경우 사용자에게 입력 요청
            const userInput = prompt(
                '🔧 ngrok 환경 설정이 필요합니다!\n\n' +
                'Admin 페이지의 ngrok URL을 입력해주세요:\n' +
                '예: https://admin-abc123.ngrok.io/admin/\n\n' +
                '💡 팁: 자주 사용한다면 코드에서 ADMIN_URL_CONFIG.ngrok을 설정하세요!'
            );

            if (userInput && userInput.trim()) {
                adminUrl = userInput.trim();
                // 입력된 URL을 임시로 저장 (새로고침하면 사라짐)
                ADMIN_URL_CONFIG.ngrok = adminUrl;
            } else {
                alert('❌ Admin URL이 입력되지 않았습니다.');
                return;
            }
        }
    } else if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
        // 로컬 환경
        adminUrl = `http://${currentHost}:9875/admin/`;
    } else {
        // 일반 외부 IP/도메인 환경
        adminUrl = `${currentProtocol}//${currentHost}:9875/admin/`;
    }

    console.log('🚀 Opening Admin URL:', adminUrl);
    window.open(adminUrl, '_blank');
}

// 페이지 로드 시 현재 환경 정보 표시
window.addEventListener('load', function() {
    const currentHost = window.location.hostname;
    if (currentHost.includes('ngrok')) {
        console.log('🌐 ngrok 환경이 감지되었습니다.');
        console.log('💡 Admin URL 설정:', ADMIN_URL_CONFIG.ngrok || '미설정 (클릭 시 입력 요청)');
    }
});
</script>

</body>
</html>
