package com.example.qaapirepository.users

import com.example.qaapirepository.dataosurce.DBConnection
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
class CardQaController(
    dbConnection: DBConnection,
) {

    val monolithServiceConnection = dbConnection.monolithServiceConnection

    @GetMapping("/users/{userId}/cards/transfer-to-normal-card")
    fun transferToNormalCard(@PathVariable userId: UUID): String {

        val query = """
            update card
            set kind = 'NORMAL_BILLING_CARD'
            where user_identifier = :userId;
        """.trimIndent()

        val executeCount = monolithServiceConnection.update(query, mapOf("userId" to userId))


        return "Transfer to normal card success. userId : $userId, execute count : $executeCount"
    }
}
