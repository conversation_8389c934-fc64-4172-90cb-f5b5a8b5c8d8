package com.example.qaapirepository.dataosurce

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

@Component
@ConfigurationProperties(prefix = "message-service.datasource")
data class MessageServiceProperties(
    var jdbcUrl: String = "",
    var username: String = "",
    var password: String = "",
    var poolName: String = "",
)
