monolith.datasource:
  pool-name: monolith_db
  username: paytalab
  password: paytalab1!
  jdbc-url: *************************************************************************************************************

event-service.datasource:
  pool-name: event_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: **************************************************************************************************************


settlement-service.datasource:
  pool-name: settlement_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: *******************************************************************************************************************


analysis-service.datasource:
  pool-name: analysis_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: ********************************************************************************************************************

reward-service.datasource:
  pool-name: reward_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: **************************************************************************************************************


auth-service.datasource:
  pool-name: auth_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: *****************************************************************************************************

passmoney-ledger-service.datasource:
  pool-name: passmoney_ledger_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: **************************************************************************************************************************

settlement-ledger-service.datasource:
  pool-name: settlement_ledger_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: **************************************************************************************************************************************

message-service.datasource:
  pool-name: message_service_db
  username: paytalab
  password: paytalab1!
  jdbc-url: ****************************************************************************************************************

external-api:
  reward-service: https://staging.passorder.me:9999/points
  store-service: https://staging.passorder.me:9999/stores
  monolith: "https://staging.passorder.me:9999"
