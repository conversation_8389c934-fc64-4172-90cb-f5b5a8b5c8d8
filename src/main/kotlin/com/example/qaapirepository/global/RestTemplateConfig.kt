package com.example.qaapirepository.global

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestTemplate


@Configuration
class RestTemplateConfig(val objectMapper: ObjectMapper) {

    @Bean
    fun mappingJacksonHttpMessageConverter(): MappingJackson2HttpMessageConverter {
        val converter = MappingJackson2HttpMessageConverter()
        converter.objectMapper = objectMapper
        return converter
    }

    @Bean
    fun restTemplate(mappingJacksonHttpMessageConverter: MappingJackson2HttpMessageConverter): RestTemplate {
        val restTemplate = RestTemplate()

        for (i in restTemplate.messageConverters.indices) {
            val httpMessageConverter: HttpMessageConverter<*> = restTemplate.messageConverters[i]
            if (httpMessageConverter is MappingJackson2HttpMessageConverter) {
                restTemplate.messageConverters[i] = mappingJacksonHttpMessageConverter
            }
        }

        return restTemplate
    }
}
