package com.example.qaapirepository.rewards

import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
class PassbankQaController(
    dbConnection: DBConnection,
) {

    val rewardServiceConnection = dbConnection.rewardServiceConnection

    @GetMapping("/rewards/passbank/deposit")
    fun depositPassbank(
        @RequestParam("userId") userId: UUID,
        @RequestParam("amount") amount: Int,
    ): String {

        val passbankAccountQueryResult = NativeQueryBuilder()
            .select("*")
            .from("passbank_account")
            .where("user_identifier = :userId")
            .addParams("userId" to userId)
            .let {
                rewardServiceConnection.queryForList(it.query, it.queryParams)
            }.singleOrNull()

        if (passbankAccountQueryResult != null) {
            val updateSuccess = NativeQueryBuilder()
                .update("passbank_account")
                .set("total_balance = total_balance + :amount")
                .where("user_identifier = :userId")
                .addParams("userId" to userId)
                .addParams("amount" to amount)
                .let {
                    rewardServiceConnection.update(it.query, it.queryParams)
                }
        }else{
            NativeQueryBuilder()
                .insert("passbank_account")
                .columns(
                    "identifier",
                    "user_identifier",
                    "total_balance",
                    "created_date",
                    "last_modified_date"
                )
                .values(":identifier", ":userId", ":amount", "now()", "now()")
                .addParams("identifier" to UUID.randomUUID())
                .addParams("userId" to userId)
                .addParams("amount" to amount)
                .let {
                    rewardServiceConnection.update(it.query, it.queryParams)
                }
        }

        val query2: MutableMap<String, Any>? = NativeQueryBuilder()
            .select("identifier, user_identifier, total_balance, created_date, last_modified_date")
            .from("passbank_account")
            .where("user_identifier = :userId")
            .addParams("userId" to userId)
            .let {
                rewardServiceConnection.queryForMap(it.query, it.queryParams)
            }


        return "Deposit success. userId : ${userId}, deposit amount : ${amount}, total_balance : ${query2?.get("total_balance")}"
    }
}
