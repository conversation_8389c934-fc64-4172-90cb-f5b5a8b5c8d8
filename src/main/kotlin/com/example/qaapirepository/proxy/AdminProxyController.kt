package com.example.qaapirepository.proxy

import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpServerErrorException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.net.URI

@Controller
@RequestMapping("/admin-proxy")
class AdminProxyController(
    private val restTemplate: RestTemplate
) {

    private val log = LoggerFactory.getLogger(this::class.java)
    
    // Admin 서비스의 실제 주소 (로컬)
    private val adminBaseUrl = "http://localhost:9875"

    @GetMapping("/**")
    fun proxyAdminRequest(
        request: HttpServletRequest,
        response: HttpServletResponse
    ): ResponseEntity<String> {
        
        val requestPath = request.requestURI.removePrefix("/admin-proxy")
        val queryString = request.queryString
        
        val targetUrl = if (queryString != null) {
            "$adminBaseUrl$requestPath?$queryString"
        } else {
            "$adminBaseUrl$requestPath"
        }
        
        log.info("Proxying request to: $targetUrl")
        
        try {
            // 원본 요청의 헤더 복사 (Host 헤더 제외)
            val headers = HttpHeaders()
            request.headerNames.asSequence()
                .filter { it.lowercase() != "host" }
                .forEach { headerName ->
                    headers.add(headerName, request.getHeader(headerName))
                }
            
            val entity = HttpEntity<String>(headers)
            
            val proxyResponse = restTemplate.exchange(
                URI.create(targetUrl),
                HttpMethod.valueOf(request.method),
                entity,
                String::class.java
            )
            
            // 응답 헤더 복사 (일부 제외)
            proxyResponse.headers.forEach { (name, values) ->
                if (name.lowercase() !in listOf("transfer-encoding", "connection", "server")) {
                    values.forEach { value ->
                        response.addHeader(name, value)
                    }
                }
            }
            
            return ResponseEntity
                .status(proxyResponse.statusCode)
                .body(proxyResponse.body)
                
        } catch (e: HttpClientErrorException) {
            log.error("Client error while proxying to admin: ${e.statusCode} - ${e.responseBodyAsString}")
            return ResponseEntity
                .status(e.statusCode)
                .body("Admin service error: ${e.message}")
                
        } catch (e: HttpServerErrorException) {
            log.error("Server error while proxying to admin: ${e.statusCode} - ${e.responseBodyAsString}")
            return ResponseEntity
                .status(e.statusCode)
                .body("Admin service error: ${e.message}")
                
        } catch (e: Exception) {
            log.error("Error while proxying to admin service", e)
            return ResponseEntity
                .internalServerError()
                .body("Failed to connect to admin service: ${e.message}")
        }
    }
}
