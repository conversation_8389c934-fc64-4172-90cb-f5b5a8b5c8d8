import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
	id 'org.jetbrains.kotlin.jvm' version '1.9.25'
	id 'org.jetbrains.kotlin.plugin.spring' version '1.9.25'
	id 'org.springframework.boot' version '3.3.5'
	id 'io.spring.dependency-management' version '1.1.6'
	id 'org.openapi.generator' version '7.0.1'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
	implementation 'org.jetbrains.kotlin:kotlin-reflect'
	runtimeOnly 'org.postgresql:postgresql'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.jetbrains.kotlin:kotlin-test-junit5'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

	// rest template
	implementation 'org.apache.httpcomponents:httpclient:4.5.13'

	// cache dependencies
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'com.github.ben-manes.caffeine:caffeine'

	// swagger dependencies
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'
	implementation 'org.springdoc:springdoc-openapi-ui:1.7.0' // 최신 버전 확인
}

kotlin {
	compilerOptions {
		freeCompilerArgs.addAll '-Xjsr305=strict'
	}
}

tasks.named('test') {
	useJUnitPlatform()
}

sourceSets {
	main {
		java.srcDirs("$buildDir/generated")
	}
}

ext {
	dirs = [
			// 명세가 위치한 경로
			'openapi-specific'       : "$rootDir/src/main/resources/openapi-specific",
			// 생성되는 코드들이 위치할 경로
			'openApiGenerate': "$buildDir/openapi"
	]
	// api, invoker, model이 위치할 패키지 경로
	openApiPackages = ['openapi.api', 'openapi.invoker', 'openapi.model']

	// 명세를 기반으로 코드를 생성하는 task들을 만들고 저장해둔다.
	generateOpenApiTasks = fileTree(dirs.get("openapi-specific"))
			.files
			.findAll { file -> (file.name.endsWith('.yaml') || file.name.endsWith('.yml')) }
			.collect(file -> createOpenApiGenerateTask(file.name))
}

def createOpenApiGenerateTask(String fileName) {
	tasks.register("openApiGenerate_$fileName", GenerateTask) {
		getGeneratorName().set("spring")
		getInputSpec().set("${dirs["openapi-specific"]}/$fileName")
		getOutputDir().set(dirs.get("openApiGenerate") as String)
		getApiPackage().set(openApiPackages[0] as String)
		getInvokerPackage().set(openApiPackages[1] as String)
		getModelPackage().set(openApiPackages[2] as String)
		// 다음 문서를 확인하여 적절한 옵션을 넣는다.
		// https://openapi-generator.tech/docs/generators/spring
		getConfigOptions().set(
				[
						"dateLibrary"    : "spring",
						"useSpringBoot3" : "true",
						"useTags"        : "true",
						"openApiNullable": "false",
						// API를 interface로 생성한다.
						"interfaceOnly"  : "true"
				]
		)
	}
}

// ext에 선언해놨던 생성된 Task들에 의존한다.
tasks.register("createOpenApi") {
	doFirst {
		println("Creating Code By OpenAPI...")
	}
	doLast {
		println("OpenAPI Code created.")
	}
	// 해당 작업은 generateOpenApiTasks에 의존한다.
	dependsOn(generateOpenApiTasks)
}


// 문서를 기반으로 생성된 코드 중 사용할 코드만 source 디렉토리로 이동한다.
tasks.register("moveGeneratedSources") {
	doFirst {
		println("Moving generated sources...")
	}

	doLast {
		openApiPackages.each { packageName ->
			def packagePath = packageName.replace(".", "/")
			def originDir = file("${dirs.get('openApiGenerate')}/src/main/java/${packagePath}")
			def destinationDir = file("$buildDir/generated/${packagePath}")
			copy {
				originDir = file("${dirs.get('openApiGenerate')}/src/main/java/${packagePath}")
				destinationDir = file("$buildDir/generated/${packagePath}")
				from originDir
				into destinationDir
			}
		}
		println 'Generated sources moved.'
	}
	// 해당 작업은 createOpenApi Task에 의존한다.
	dependsOn("createOpenApi")
}

// 문서를 기반으로 생성된 불필요한 코드들을 제거한다.
tasks.register("cleanGeneratedDirectory") {
	doFirst {
		println("Cleaning generated directory...")
	}
	doLast {
		def openApiGenerateDir = file(dirs.get('openApiGenerate'))
		if (openApiGenerateDir.exists()) {
			openApiGenerateDir.deleteDir()
			println "Directory ${openApiGenerateDir} deleted."
		} else {
			println "Directory ${openApiGenerateDir} does not exist."
		}
	}
	// 해당 작업은 moveGeneratedSources에 의존한다.
	dependsOn("moveGeneratedSources")
}

tasks.named("compileJava") {
	// 컴파일 이전에 코드 생성작업이 수행된다.
	dependsOn("cleanGeneratedDirectory")
}
