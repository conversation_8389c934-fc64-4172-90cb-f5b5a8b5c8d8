package com.example.qaapirepository.passmoney

import com.example.qaapirepository.dataosurce.DBConnection
import com.example.qaapirepository.global.querybuilder.NativeQueryBuilder
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.util.UUID

@RestController
class PassmoneyQaController(
    dbConnection: DBConnection,
) {


    val passmoneyLedgerServiceConnection = dbConnection.passmoneyLedgerServiceConnection


    // 패스머니 입금 api
    @GetMapping("/passmoneys/deposits")
    fun depositPassmoney(
        @RequestParam("userId") userId: UUID,
        @RequestParam("amount") amount: Int,
    ): String {

        val resultMap = NativeQueryBuilder()
            .select("total_price")
            .from("passmoney")
            .where("user_identifier = :user_identifier")
            .addParams("user_identifier" to userId)
            .orderBy("created_date desc")
            .limit(1)
            .let { passmoneyLedgerServiceConnection.queryForList(it.query, it.queryParams) }

        val firstOrNull = resultMap.firstOrNull()

        val totalPriceAny = firstOrNull?.get("total_price")

        println("totalPriceAny : ${totalPriceAny}")
        if (totalPriceAny != null) {
            print(totalPriceAny::class.qualifiedName)
        }

        val totalPrice = totalPriceAny as? Int

        val nextTotalPrice = totalPrice?.plus(amount) ?: amount

        NativeQueryBuilder()
            .insert("passmoney")
            .columns(
                "identifier",
                "created_date",
                "description",
                "expiry_date",
                "order_identifier",
                "passmoney_charge_account_identifier",
                "passmoney_purchase_policy_identifier",
                "price",
                "status",
                "tid",
                "given_reward_amount",
                "total_price",
                "used_price",
                "user_identifier",
            )
            .values(
                """'${UUID.randomUUID()}'""",
                "now()",
                "'QA 입금'",
                "now() + interval '5 years'",
                null,
                null,
                null,
                amount,
                "'CHARGED'",
                null,
                0,
                nextTotalPrice,
                0,
                "'${userId}'"
            )
            .let {
                println(it.query)
                println(it.queryParams)
                passmoneyLedgerServiceConnection.update(it.query, it.queryParams) }

        return """
            ===========================================================================================================================
            | 입금이 완료되었습니다. userId: $userId, amount: $amount, totalPrice: $nextTotalPrice
            ===========================================================================================================================
        """.trimIndent()
    }

}
