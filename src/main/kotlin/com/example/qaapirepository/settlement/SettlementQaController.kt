package com.example.qaapirepository.settlement

import com.example.qaapirepository.dataosurce.DBConnection
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
class SettlementQaController(
    dbConnection: DBConnection,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    val settlementServiceConnection: NamedParameterJdbcTemplate = dbConnection.settlementServiceConnection
    val analysisServiceConnection: NamedParameterJdbcTemplate = dbConnection.analysisServiceConnection
    val settlementLedgerConnection: NamedParameterJdbcTemplate = dbConnection.settlementLedgerConnection

    @GetMapping("/settlement/move-settlement-date-of-store")
    fun moveSettlementDateOfStore(
        @RequestParam("storeId") storeId: UUID,
        @RequestParam("movedDays") movedDays: String,
    ): String {

        log.info("Invoke moveSettlementDateOfStore. storeId : ${storeId}, movedDays : ${movedDays}")

        val sql = """
        update order_settlement
        set sales_date = sales_date + interval '${movedDays} days'
        where store_id = :storeId;
        """.trimIndent()

        val params = mapOf(
            "storeId" to storeId,
        )

        val sql2 = """
            update order_settlement
            set sales_date = sales_date + interval '${movedDays} days'
            where store_identifier = :storeId;
        """.trimIndent()

        val sql3 = """
            update order_settlement_ledger
            set sales_date = sales_date + interval '${movedDays} days'
            where store_identifier = :storeId;
        """.trimIndent()

        val sql4 = """
            update store_daily_settlement
            set sales_date = sales_date + interval '${movedDays} days'
            where store_identifier = :storeId;
        """.trimIndent()

        val sql5 = """
            update store_daily_settlement_payment
            set sales_date = sales_date + interval '${movedDays} days'
            where store_identifier = :storeId;
        """.trimIndent()

        val executeCount = settlementServiceConnection.update(sql, params)

        val executeCount2 = analysisServiceConnection.update(sql2, params)


        val executeCount3 = settlementLedgerConnection.update(sql3, params)
        val executeCount4 = settlementLedgerConnection.update(sql4, params)
        val executeCount5 = settlementLedgerConnection.update(sql5, params)


        return """
            Update order settlement success. storeId : ${storeId}, movedDays = ${movedDays}.
            
            Moved settlement [${executeCount}] rows
            
            Moved analysis [${executeCount2}] rows
            
            Moved settlement ledger [${executeCount3}] rows
            Moved store daily settlement [${executeCount4}] rows
            Moved store daily settlement payment [${executeCount5}] rows
        """.trimIndent()
    }
}
