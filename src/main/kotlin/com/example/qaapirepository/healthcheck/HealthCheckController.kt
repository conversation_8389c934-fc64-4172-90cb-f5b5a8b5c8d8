package com.example.qaapirepository.healthcheck

import org.springframework.core.io.ClassPathResource
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController

@RestController
class HealthCheckController {

    @GetMapping("/health")
    fun healthCheck(): String {
        // get spring running port from env


        return "Health check success."
    }

    @GetMapping("/favicon.ico")
    fun favicon(): ResponseEntity<String> {
        return ResponseEntity.ok("favicon")
    }
}
