openapi: 3.0.0
info:
  title: 회원 API
  version: 1.0.0
  description: 회원 관리를 위한 API 문서
paths:
  /members:
    post:
      summary: 회원 생성
      description: 새로운 회원을 시스템에 등록합니다.
      tags:
        - MembersController
      requestBody:
        description: 생성할 회원의 정보
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemberRequestDto'
      responses:
        '201':
          description: 회원이 성공적으로 생성되었습니다.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemberResponseDto'
        '400':
          description: 잘못된 입력 데이터
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 이메일 형식이 유효하지 않습니다.
        '500':
          description: 서버 오류
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: 서버 내부 오류
components:
  schemas:
    MemberRequestDto:
      type: object
      properties:
        username:
          type: string
          example: hong_gildong
          description: 생성할 회원의 사용자 이름
        email:
          type: string
          example: <EMAIL>
          description: 생성할 회원의 이메일 주소
        password:
          type: string
          format: password
          example: password123!
          description: 생성할 회원의 비밀번호
      required:
        - username
        - email
        - password
    MemberResponseDto:
      type: object
      properties:
        id:
          type: integer
          example: 1
          description: 생성된 회원의 고유 ID
        username:
          type: string
          example: hong_gildong
          description: 생성된 회원의 사용자 이름
        email:
          type: string
          example: <EMAIL>
          description: 생성된 회원의 이메일 주소
