package com.example.qaapirepository.dataosurce

import org.springframework.beans.factory.annotation.Value
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate
import org.springframework.stereotype.Component
import javax.sql.DataSource

@Component
class DBConnection(
    monolithServiceProperties: MonolithServiceProperties,
    eventServiceProperties: EventServiceProperties,
    settlementServiceProperties: SettlementServiceProperties,
    analysisServiceProperties: AnalysisServiceProperties,
    rewardServiceProperties: RewardServiceProperties,
    authServiceProperties: AuthServiceProperties,
    passmoneyLedgerServiceProperties: PassmoneyLedgerServiceProperties,
    settlementLedgerProperties: SettlementLedgerProperties,
    messageServiceProperties: MessageServiceProperties,
) {

    private final val monolithServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = monolithServiceProperties.jdbcUrl,
        username = monolithServiceProperties.username,
        password = monolithServiceProperties.password,
        poolName = monolithServiceProperties.poolName,
    )

    val monolithServiceConnection = NamedParameterJdbcTemplate(monolithServiceDataSource)

    private final val eventServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = eventServiceProperties.jdbcUrl,
        username = eventServiceProperties.username,
        password = eventServiceProperties.password,
        poolName = eventServiceProperties.poolName,
    )

    val eventServiceConnection = NamedParameterJdbcTemplate(eventServiceDataSource)

    private final val settlementServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = settlementServiceProperties.jdbcUrl,
        username = settlementServiceProperties.username,
        password = settlementServiceProperties.password,
        poolName = settlementServiceProperties.poolName,
    )

    val settlementServiceConnection = NamedParameterJdbcTemplate(settlementServiceDataSource)

    private final val analysisServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = analysisServiceProperties.jdbcUrl,
        username = analysisServiceProperties.username,
        password = analysisServiceProperties.password,
        poolName = analysisServiceProperties.poolName,
    )

    val analysisServiceConnection = NamedParameterJdbcTemplate(analysisServiceDataSource)

    private final val rewardServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = rewardServiceProperties.jdbcUrl,
        username = rewardServiceProperties.username,
        password = rewardServiceProperties.password,
        poolName = rewardServiceProperties.poolName,
    )

    val rewardServiceConnection = NamedParameterJdbcTemplate(rewardServiceDataSource)

    private final val authServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = authServiceProperties.jdbcUrl,
        username = authServiceProperties.username,
        password = authServiceProperties.password,
        poolName = authServiceProperties.poolName,
    )

    val authServiceConnection = NamedParameterJdbcTemplate(authServiceDataSource)

    private final val passmoneyLedgerServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = passmoneyLedgerServiceProperties.jdbcUrl,
        username = passmoneyLedgerServiceProperties.username,
        password = passmoneyLedgerServiceProperties.password,
        poolName = passmoneyLedgerServiceProperties.poolName,
    )

    val passmoneyLedgerServiceConnection = NamedParameterJdbcTemplate(passmoneyLedgerServiceDataSource)

    private final val settlementLedgerDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = settlementLedgerProperties.jdbcUrl,
        username = settlementLedgerProperties.username,
        password = settlementLedgerProperties.password,
        poolName = settlementLedgerProperties.poolName,
    )

    val settlementLedgerConnection = NamedParameterJdbcTemplate(settlementLedgerDataSource)

    private final val messageServiceDataSource: DataSource = DatasourceFactory.createDatasource(
        jdbcUrl = messageServiceProperties.jdbcUrl,
        username = messageServiceProperties.username,
        password = messageServiceProperties.password,
        poolName = messageServiceProperties.poolName,
    )

    val messageServiceConnection = NamedParameterJdbcTemplate(messageServiceDataSource)
}

@Component
class MonolithServiceProperties(
    @Value("\${monolith.datasource.pool-name}")
    val poolName: String,
    @Value("\${monolith.datasource.password}")
    val password: String,
    @Value("\${monolith.datasource.username}")
    val username: String,
    @Value("\${monolith.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class EventServiceProperties(
    @Value("\${event-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${event-service.datasource.password}")
    val password: String,
    @Value("\${event-service.datasource.username}")
    val username: String,
    @Value("\${event-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class SettlementServiceProperties(
    @Value("\${settlement-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${settlement-service.datasource.password}")
    val password: String,
    @Value("\${settlement-service.datasource.username}")
    val username: String,
    @Value("\${settlement-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class AnalysisServiceProperties(
    @Value("\${analysis-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${analysis-service.datasource.password}")
    val password: String,
    @Value("\${analysis-service.datasource.username}")
    val username: String,
    @Value("\${analysis-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)


@Component
class  RewardServiceProperties(
    @Value("\${reward-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${reward-service.datasource.password}")
    val password: String,
    @Value("\${reward-service.datasource.username}")
    val username: String,
    @Value("\${reward-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class  AuthServiceProperties(
    @Value("\${auth-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${auth-service.datasource.password}")
    val password: String,
    @Value("\${auth-service.datasource.username}")
    val username: String,
    @Value("\${auth-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class PassmoneyLedgerServiceProperties(
    @Value("\${passmoney-ledger-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${passmoney-ledger-service.datasource.password}")
    val password: String,
    @Value("\${passmoney-ledger-service.datasource.username}")
    val username: String,
    @Value("\${passmoney-ledger-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)

@Component
class SettlementLedgerProperties(
    @Value("\${settlement-ledger-service.datasource.pool-name}")
    val poolName: String,
    @Value("\${settlement-ledger-service.datasource.password}")
    val password: String,
    @Value("\${settlement-ledger-service.datasource.username}")
    val username: String,
    @Value("\${settlement-ledger-service.datasource.jdbc-url}")
    val jdbcUrl: String,
)
